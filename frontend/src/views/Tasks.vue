<template>
  <div class="tasks">
    <div class="page-header">
      <h1>任务管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        创建任务
      </el-button>
    </div>
    
    <!-- 搜索和过滤 -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchQuery"
            placeholder="搜索任务名称"
            prefix-icon="Search"
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="6">
          <el-select v-model="statusFilter" placeholder="任务状态" @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="活跃" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
            <el-option label="暂停" value="paused" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="refreshTasks">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 任务列表 -->
    <el-card>
      <el-table
        v-loading="loading"
        :data="tasks"
        style="width: 100%"
      >
        <el-table-column prop="name" label="任务名称" width="200" />
        <el-table-column prop="url" label="目标URL" min-width="300" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="last_run" label="最后运行" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.last_run) }}
          </template>
        </el-table-column>
        <el-table-column prop="next_run" label="下次运行" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.next_run) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="executeTask(row)">
              <el-icon><VideoPlay /></el-icon>
              执行
            </el-button>
            <el-button size="small" type="primary" @click="editTask(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteTask(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 创建/编辑任务对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingTask ? '编辑任务' : '创建任务'"
      width="600px"
    >
      <el-form
        ref="taskFormRef"
        :model="taskForm"
        :rules="taskRules"
        label-width="100px"
      >
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="taskForm.name" />
        </el-form-item>
        
        <el-form-item label="目标URL" prop="url">
          <el-input v-model="taskForm.url" />
        </el-form-item>
        
        <el-form-item label="提取规则" prop="extraction_rules">
          <el-input
            v-model="taskForm.extraction_rules"
            type="textarea"
            :rows="4"
            placeholder="JSON格式的提取规则"
          />
        </el-form-item>
        
        <el-form-item label="调度配置">
          <el-input
            v-model="taskForm.schedule_config"
            type="textarea"
            :rows="3"
            placeholder="JSON格式的调度配置（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveTask" :loading="saving">
          {{ saving ? '保存中...' : '保存' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/utils/api'

const loading = ref(false)
const saving = ref(false)
const tasks = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const searchQuery = ref('')
const statusFilter = ref('')

const showCreateDialog = ref(false)
const editingTask = ref(null)
const taskFormRef = ref()

const taskForm = reactive({
  name: '',
  url: '',
  extraction_rules: '',
  schedule_config: ''
})

const taskRules = {
  name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  url: [{ required: true, message: '请输入目标URL', trigger: 'blur' }],
  extraction_rules: [{ required: true, message: '请输入提取规则', trigger: 'blur' }]
}

const getStatusType = (status) => {
  const typeMap = {
    active: 'success',
    completed: 'info',
    failed: 'danger',
    paused: 'warning'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    active: '活跃',
    completed: '已完成',
    failed: '失败',
    paused: '暂停'
  }
  return textMap[status] || status
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const loadTasks = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value
    }
    
    if (searchQuery.value) {
      params.search = searchQuery.value
    }
    
    if (statusFilter.value) {
      params.status = statusFilter.value
    }
    
    const response = await api.get('/tasks/', { params })
    tasks.value = response.data.tasks
    total.value = response.data.pagination.total
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadTasks()
}

const handleFilter = () => {
  currentPage.value = 1
  loadTasks()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadTasks()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadTasks()
}

const refreshTasks = () => {
  loadTasks()
}

const executeTask = async (task) => {
  try {
    await api.post(`/tasks/${task.id}/execute/`)
    ElMessage.success('任务已提交执行')
    loadTasks()
  } catch (error) {
    ElMessage.error('执行任务失败')
  }
}

const editTask = (task) => {
  editingTask.value = task
  taskForm.name = task.name
  taskForm.url = task.url
  taskForm.extraction_rules = JSON.stringify(task.extraction_rules, null, 2)
  taskForm.schedule_config = JSON.stringify(task.schedule_config || {}, null, 2)
  showCreateDialog.value = true
}

const deleteTask = async (task) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {
      type: 'warning'
    })
    
    await api.delete(`/tasks/${task.id}/`)
    ElMessage.success('任务删除成功')
    loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
    }
  }
}

const saveTask = async () => {
  if (!taskFormRef.value) return
  
  await taskFormRef.value.validate(async (valid) => {
    if (valid) {
      saving.value = true
      
      try {
        const data = {
          name: taskForm.name,
          url: taskForm.url,
          extraction_rules: JSON.parse(taskForm.extraction_rules),
          schedule_config: taskForm.schedule_config ? JSON.parse(taskForm.schedule_config) : null
        }
        
        if (editingTask.value) {
          await api.put(`/tasks/${editingTask.value.id}/`, data)
          ElMessage.success('任务更新成功')
        } else {
          await api.post('/tasks/', data)
          ElMessage.success('任务创建成功')
        }
        
        showCreateDialog.value = false
        resetForm()
        loadTasks()
      } catch (error) {
        ElMessage.error('保存任务失败')
      } finally {
        saving.value = false
      }
    }
  })
}

const resetForm = () => {
  editingTask.value = null
  taskForm.name = ''
  taskForm.url = ''
  taskForm.extraction_rules = ''
  taskForm.schedule_config = ''
}

onMounted(() => {
  loadTasks()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
}

.filter-card {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
