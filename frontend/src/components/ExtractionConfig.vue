<template>
  <div class="extraction-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>数据提取配置</span>
          <el-button type="primary" @click="saveConfig" :loading="saving">
            {{ saving ? "保存中..." : "保存配置" }}
          </el-button>
        </div>
      </template>

      <el-form
        :model="config"
        :rules="rules"
        ref="configForm"
        label-width="120px"
      >
        <!-- 基础配置 -->
        <el-divider content-position="left">基础配置</el-divider>

        <el-form-item label="任务名称" prop="name">
          <el-input v-model="config.name" placeholder="请输入任务名称" />
        </el-form-item>

        <el-form-item label="目标URL" prop="url">
          <el-input
            v-model="config.url"
            placeholder="请输入要提取数据的网页URL"
          />
        </el-form-item>

        <el-form-item label="提取类型">
          <el-radio-group v-model="config.extractionType">
            <el-radio label="auto">自动识别</el-radio>
            <el-radio label="manual">手动配置</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 自动识别配置 -->
        <div v-if="config.extractionType === 'auto'">
          <el-divider content-position="left">自动识别配置</el-divider>

          <el-form-item label="识别元素">
            <el-checkbox-group v-model="config.autoConfig.elements">
              <el-checkbox label="tables">表格数据</el-checkbox>
              <el-checkbox label="cards">卡片数据</el-checkbox>
              <el-checkbox label="lists">列表数据</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="置信度阈值">
            <el-slider
              v-model="config.autoConfig.confidenceThreshold"
              :min="0.1"
              :max="1.0"
              :step="0.1"
              show-stops
              show-input
            />
          </el-form-item>
        </div>

        <!-- 手动配置 -->
        <div v-if="config.extractionType === 'manual'">
          <el-divider content-position="left">手动提取规则</el-divider>

          <div
            v-for="(rule, index) in config.manualRules"
            :key="index"
            class="rule-item"
          >
            <el-card class="rule-card">
              <template #header>
                <div class="rule-header">
                  <span>规则 {{ index + 1 }}</span>
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeRule(index)"
                    :disabled="config.manualRules.length === 1"
                  >
                    删除
                  </el-button>
                </div>
              </template>

              <el-form-item label="字段名称">
                <el-input v-model="rule.fieldName" placeholder="数据字段名称" />
              </el-form-item>

              <el-form-item label="选择器类型">
                <el-select v-model="rule.selectorType" placeholder="选择器类型">
                  <el-option label="CSS选择器" value="css" />
                  <el-option label="XPath" value="xpath" />
                </el-select>
              </el-form-item>

              <el-form-item label="选择器">
                <el-input
                  v-model="rule.selector"
                  placeholder="请输入CSS选择器或XPath"
                  type="textarea"
                  :rows="2"
                />
              </el-form-item>

              <el-form-item label="提取属性">
                <el-select
                  v-model="rule.attribute"
                  placeholder="选择要提取的属性"
                >
                  <el-option label="文本内容" value="text" />
                  <el-option label="HTML内容" value="html" />
                  <el-option label="href属性" value="href" />
                  <el-option label="src属性" value="src" />
                  <el-option label="自定义属性" value="custom" />
                </el-select>
              </el-form-item>

              <el-form-item
                v-if="rule.attribute === 'custom'"
                label="自定义属性名"
              >
                <el-input
                  v-model="rule.customAttribute"
                  placeholder="属性名称"
                />
              </el-form-item>

              <el-form-item label="数据处理">
                <el-checkbox-group v-model="rule.processing">
                  <el-checkbox label="trim">去除空白</el-checkbox>
                  <el-checkbox label="lowercase">转小写</el-checkbox>
                  <el-checkbox label="removeHtml">移除HTML标签</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-card>
          </div>

          <el-button @click="addRule" type="dashed" class="add-rule-btn">
            <el-icon><Plus /></el-icon>
            添加提取规则
          </el-button>
        </div>

        <!-- 高级配置 -->
        <el-divider content-position="left">高级配置</el-divider>

        <el-form-item label="页面等待">
          <el-input-number
            v-model="config.waitConfig.timeout"
            :min="1000"
            :max="60000"
            :step="1000"
            controls-position="right"
          />
          <span class="input-suffix">毫秒</span>
        </el-form-item>

        <el-form-item label="等待元素">
          <el-input
            v-model="config.waitConfig.waitForSelector"
            placeholder="等待特定元素加载完成（可选）"
          />
        </el-form-item>

        <el-form-item label="认证配置">
          <el-switch v-model="config.authEnabled" />
        </el-form-item>

        <div v-if="config.authEnabled">
          <el-form-item label="认证类型">
            <el-select v-model="config.authConfig.type">
              <el-option label="基础认证" value="basic" />
              <el-option label="表单登录" value="form" />
              <el-option label="Cookie" value="cookie" />
            </el-select>
          </el-form-item>

          <div v-if="config.authConfig.type === 'basic'">
            <el-form-item label="用户名">
              <el-input v-model="config.authConfig.username" />
            </el-form-item>
            <el-form-item label="密码">
              <el-input
                v-model="config.authConfig.password"
                type="password"
                show-password
              />
            </el-form-item>
          </div>

          <div v-if="config.authConfig.type === 'form'">
            <el-form-item label="登录URL">
              <el-input v-model="config.authConfig.loginUrl" />
            </el-form-item>
            <el-form-item label="用户名字段">
              <el-input
                v-model="config.authConfig.usernameField"
                placeholder="用户名输入框的name或id"
              />
            </el-form-item>
            <el-form-item label="密码字段">
              <el-input
                v-model="config.authConfig.passwordField"
                placeholder="密码输入框的name或id"
              />
            </el-form-item>
            <el-form-item label="用户名">
              <el-input v-model="config.authConfig.username" />
            </el-form-item>
            <el-form-item label="密码">
              <el-input
                v-model="config.authConfig.password"
                type="password"
                show-password
              />
            </el-form-item>
          </div>

          <div v-if="config.authConfig.type === 'cookie'">
            <el-form-item label="Cookie">
              <el-input
                v-model="config.authConfig.cookies"
                type="textarea"
                :rows="3"
                placeholder="请输入Cookie字符串"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 调度配置 -->
        <el-form-item label="定时执行">
          <el-switch v-model="config.scheduleEnabled" />
        </el-form-item>

        <div v-if="config.scheduleEnabled">
          <el-form-item label="执行频率">
            <el-select v-model="config.scheduleConfig.type">
              <el-option label="每小时" value="hourly" />
              <el-option label="每天" value="daily" />
              <el-option label="每周" value="weekly" />
              <el-option label="自定义Cron" value="cron" />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="config.scheduleConfig.type === 'cron'"
            label="Cron表达式"
          >
            <el-input
              v-model="config.scheduleConfig.cron"
              placeholder="0 0 * * *"
            />
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <!-- 预览和测试 -->
    <el-card class="preview-card">
      <template #header>
        <div class="preview-header">
          <span>配置预览和测试</span>
          <div class="header-actions">
            <el-button
              @click="loadPagePreview"
              :loading="loadingPreview"
              size="small"
            >
              <el-icon><View /></el-icon>
              加载页面预览
            </el-button>
            <el-button @click="exportConfig" size="small">
              <el-icon><Download /></el-icon>
              导出配置
            </el-button>
            <el-upload
              :auto-upload="false"
              :show-file-list="false"
              accept=".json"
              :on-change="importConfig"
            >
              <el-button size="small">
                <el-icon><Upload /></el-icon>
                导入配置
              </el-button>
            </el-upload>
          </div>
        </div>
      </template>

      <el-tabs v-model="previewTab">
        <el-tab-pane label="页面预览" name="preview">
          <div class="page-preview-container">
            <div class="preview-toolbar">
              <el-input
                v-model="config.url"
                placeholder="输入要预览的URL"
                class="url-input"
                @keyup.enter="loadPagePreview"
              >
                <template #append>
                  <el-button @click="loadPagePreview" :loading="loadingPreview"
                    >加载</el-button
                  >
                </template>
              </el-input>
              <div class="preview-controls">
                <el-tooltip content="启用选择器模式">
                  <el-button
                    :type="selectorMode ? 'primary' : 'default'"
                    @click="toggleSelectorMode"
                    size="small"
                  >
                    <el-icon><Aim /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="刷新页面">
                  <el-button @click="refreshPreview" size="small">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </el-tooltip>
              </div>
            </div>

            <div class="preview-content" v-loading="loadingPreview">
              <div v-if="!previewUrl" class="preview-placeholder">
                <el-icon size="48"><Document /></el-icon>
                <p>输入URL并点击加载按钮预览页面</p>
              </div>
              <iframe
                v-else
                ref="previewIframe"
                :src="previewUrl"
                class="preview-iframe"
                @load="onPreviewLoad"
              />
            </div>

            <div v-if="selectorMode" class="selector-panel">
              <el-alert
                title="选择器模式已启用"
                type="info"
                description="点击页面中的元素自动生成CSS选择器"
                show-icon
                :closable="false"
              />
              <div v-if="generatedSelector" class="generated-selector">
                <el-input v-model="generatedSelector" readonly size="small">
                  <template #prepend>生成的选择器:</template>
                  <template #append>
                    <el-button
                      @click="useGeneratedSelector"
                      size="small"
                      type="primary"
                      >使用</el-button
                    >
                  </template>
                </el-input>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="JSON配置" name="json">
          <div class="json-container">
            <div class="json-toolbar">
              <el-button @click="validateConfig" size="small">
                <el-icon><CircleCheck /></el-icon>
                验证配置
              </el-button>
              <el-button @click="copyConfig" size="small">
                <el-icon><CopyDocument /></el-icon>
                复制配置
              </el-button>
            </div>
            <pre class="json-preview">{{
              JSON.stringify(getConfigForPreview(), null, 2)
            }}</pre>
            <div v-if="configValidation" class="validation-result">
              <el-alert
                :title="configValidation.valid ? '配置有效' : '配置错误'"
                :type="configValidation.valid ? 'success' : 'error'"
                :description="configValidation.message"
                show-icon
                :closable="false"
              />
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="测试提取" name="test">
          <div class="test-container">
            <div class="test-toolbar">
              <el-button
                @click="testExtraction"
                :loading="testing"
                type="primary"
              >
                <el-icon><PlayFilled /></el-icon>
                {{ testing ? "测试中..." : "开始测试" }}
              </el-button>
              <el-button @click="clearTestResult" :disabled="!testResult">
                <el-icon><Delete /></el-icon>
                清空结果
              </el-button>
            </div>

            <div v-if="testing" class="test-progress">
              <el-progress :percentage="testProgress" :status="testStatus" />
              <p>{{ testMessage }}</p>
            </div>

            <div v-if="testResult" class="test-result">
              <div class="result-header">
                <h4>测试结果</h4>
                <div class="result-stats">
                  <el-tag v-if="testResult.success" type="success">成功</el-tag>
                  <el-tag v-else type="danger">失败</el-tag>
                  <span class="result-count"
                    >提取到
                    {{
                      testResult.data ? Object.keys(testResult.data).length : 0
                    }}
                    个字段</span
                  >
                  <span class="result-time"
                    >耗时: {{ testResult.duration }}ms</span
                  >
                </div>
              </div>

              <el-tabs type="border-card">
                <el-tab-pane label="提取数据" name="data">
                  <pre class="result-data">{{
                    JSON.stringify(testResult.data, null, 2)
                  }}</pre>
                </el-tab-pane>
                <el-tab-pane label="详细日志" name="logs">
                  <div class="result-logs">
                    <div
                      v-for="(log, index) in testResult.logs"
                      :key="index"
                      class="log-item"
                    >
                      <el-tag :type="getLogType(log.level)" size="small">{{
                        log.level
                      }}</el-tag>
                      <span class="log-time">{{ log.timestamp }}</span>
                      <span class="log-message">{{ log.message }}</span>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane
                  v-if="testResult.errors"
                  label="错误信息"
                  name="errors"
                >
                  <div class="result-errors">
                    <el-alert
                      v-for="(error, index) in testResult.errors"
                      :key="index"
                      :title="error.type"
                      :description="error.message"
                      type="error"
                      show-icon
                    />
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus,
  View,
  Download,
  Upload,
  Aim,
  Refresh,
  Document,
  CircleCheck,
  CopyDocument,
  PlayFilled,
  Delete,
} from "@element-plus/icons-vue";
import api from "../utils/api";

export default {
  name: "ExtractionConfig",
  components: {
    Plus,
    View,
    Download,
    Upload,
    Aim,
    Refresh,
    Document,
    CircleCheck,
    CopyDocument,
    PlayFilled,
    Delete,
  },
  props: {
    taskId: {
      type: String,
      default: null,
    },
    initialConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ["config-saved"],
  setup(props, { emit }) {
    const configForm = ref(null);
    const previewIframe = ref(null);
    const saving = ref(false);
    const testing = ref(false);
    const loadingPreview = ref(false);
    const previewTab = ref("preview");
    const testResult = ref(null);
    const previewUrl = ref("");
    const selectorMode = ref(false);
    const generatedSelector = ref("");
    const configValidation = ref(null);
    const testProgress = ref(0);
    const testStatus = ref("");
    const testMessage = ref("");

    const config = reactive({
      name: "",
      url: "",
      extractionType: "auto",
      autoConfig: {
        elements: ["tables", "cards"],
        confidenceThreshold: 0.7,
      },
      manualRules: [
        {
          fieldName: "",
          selectorType: "css",
          selector: "",
          attribute: "text",
          customAttribute: "",
          processing: ["trim"],
        },
      ],
      waitConfig: {
        timeout: 10000,
        waitForSelector: "",
      },
      authEnabled: false,
      authConfig: {
        type: "basic",
        username: "",
        password: "",
        loginUrl: "",
        usernameField: "username",
        passwordField: "password",
        cookies: "",
      },
      scheduleEnabled: false,
      scheduleConfig: {
        type: "daily",
        cron: "0 0 * * *",
      },
    });

    const rules = {
      name: [{ required: true, message: "请输入任务名称", trigger: "blur" }],
      url: [
        { required: true, message: "请输入目标URL", trigger: "blur" },
        { type: "url", message: "请输入有效的URL", trigger: "blur" },
      ],
    };

    // 初始化配置
    if (props.initialConfig && Object.keys(props.initialConfig).length > 0) {
      Object.assign(config, props.initialConfig);
    }

    const addRule = () => {
      config.manualRules.push({
        fieldName: "",
        selectorType: "css",
        selector: "",
        attribute: "text",
        customAttribute: "",
        processing: ["trim"],
      });
    };

    const removeRule = (index) => {
      if (config.manualRules.length > 1) {
        config.manualRules.splice(index, 1);
      }
    };

    const getConfigForPreview = () => {
      const previewConfig = { ...config };

      if (config.extractionType === "auto") {
        previewConfig.extraction_rules = {
          type: "auto",
          elements: config.autoConfig.elements,
          confidence_threshold: config.autoConfig.confidenceThreshold,
        };
      } else {
        previewConfig.extraction_rules = {
          type: "manual",
          rules: config.manualRules.map((rule) => ({
            field_name: rule.fieldName,
            selector_type: rule.selectorType,
            selector: rule.selector,
            attribute:
              rule.attribute === "custom"
                ? rule.customAttribute
                : rule.attribute,
            processing: rule.processing,
          })),
        };
      }

      if (config.authEnabled) {
        previewConfig.auth_config = config.authConfig;
      }

      if (config.scheduleEnabled) {
        previewConfig.schedule_config = config.scheduleConfig;
      }

      previewConfig.wait_config = config.waitConfig;

      // 清理不需要的字段
      delete previewConfig.extractionType;
      delete previewConfig.autoConfig;
      delete previewConfig.manualRules;
      delete previewConfig.authEnabled;
      delete previewConfig.scheduleEnabled;
      delete previewConfig.waitConfig;
      delete previewConfig.authConfig;
      delete previewConfig.scheduleConfig;

      return previewConfig;
    };

    const saveConfig = async () => {
      try {
        await configForm.value.validate();
        saving.value = true;

        const configData = getConfigForPreview();

        let response;
        if (props.taskId) {
          // 更新现有任务
          response = await api.put(`/tasks/${props.taskId}/`, configData);
        } else {
          // 创建新任务
          response = await api.post("/tasks/", configData);
        }

        ElMessage.success("配置保存成功");
        emit("config-saved", response.data);
      } catch (error) {
        console.error("保存配置失败:", error);
        ElMessage.error(
          "保存配置失败: " + (error.response?.data?.detail || error.message),
        );
      } finally {
        saving.value = false;
      }
    };

    const testExtraction = async () => {
      try {
        testing.value = true;
        testResult.value = null;

        const configData = getConfigForPreview();
        const response = await api.post("/extract/test", {
          url: config.url,
          extraction_rules: configData.extraction_rules,
          auth_config: configData.auth_config,
          wait_config: configData.wait_config,
        });

        testResult.value = response.data;
        ElMessage.success("测试完成");
      } catch (error) {
        console.error("测试失败:", error);
        ElMessage.error(
          "测试失败: " + (error.response?.data?.detail || error.message),
        );
      } finally {
        testing.value = false;
      }
    };

    const loadPagePreview = async () => {
      if (!config.url) {
        ElMessage.warning("请先输入目标URL");
        return;
      }

      try {
        loadingPreview.value = true;
        // 使用代理服务加载页面预览
        const response = await api.post("/extract/preview", {
          url: config.url,
          auth_config: config.authEnabled ? config.authConfig : null,
        });

        if (response.data.preview_url) {
          previewUrl.value = response.data.preview_url;
        }

        ElMessage.success("页面预览加载成功");
      } catch (error) {
        console.error("加载页面预览失败:", error);
        ElMessage.error(
          "加载页面预览失败: " +
            (error.response?.data?.detail || error.message),
        );
      } finally {
        loadingPreview.value = false;
      }
    };

    const refreshPreview = () => {
      if (previewIframe.value) {
        previewIframe.value.src = previewIframe.value.src;
      }
    };

    const toggleSelectorMode = () => {
      selectorMode.value = !selectorMode.value;
      generatedSelector.value = "";

      if (selectorMode.value) {
        // 启用选择器模式时的逻辑
        ElMessage.info("选择器模式已启用，点击页面中的元素生成选择器");
        enableSelectorMode();
      } else {
        ElMessage.info("选择器模式已关闭");
        disableSelectorMode();
      }
    };

    const enableSelectorMode = () => {
      // 这里需要与iframe通信，添加点击监听器
      if (previewIframe.value) {
        // 注入JavaScript到iframe中
        const script = `
          document.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const element = e.target;
            const selector = generateSelector(element);

            window.parent.postMessage({
              type: 'element-selected',
              selector: selector,
              element: {
                tagName: element.tagName,
                className: element.className,
                id: element.id,
                textContent: element.textContent.substring(0, 50)
              }
            }, '*');
          });

          function generateSelector(element) {
            if (element.id) {
              return '#' + element.id;
            }

            let selector = element.tagName.toLowerCase();

            if (element.className) {
              selector += '.' + element.className.split(' ').join('.');
            }

            let parent = element.parentElement;
            if (parent) {
              const siblings = Array.from(parent.children);
              const index = siblings.indexOf(element);
              if (siblings.length > 1) {
                selector += ':nth-child(' + (index + 1) + ')';
              }
            }

            return selector;
          }
        `;

        try {
          previewIframe.value.contentWindow.eval(script);
        } catch (error) {
          console.error("注入选择器脚本失败:", error);
        }
      }
    };

    const disableSelectorMode = () => {
      // 移除iframe中的事件监听器
      if (previewIframe.value) {
        try {
          previewIframe.value.contentWindow.location.reload();
        } catch (error) {
          console.error("重载iframe失败:", error);
        }
      }
    };

    const onPreviewLoad = () => {
      if (selectorMode.value) {
        setTimeout(() => {
          enableSelectorMode();
        }, 1000);
      }
    };

    const useGeneratedSelector = () => {
      if (!generatedSelector.value) return;

      if (config.extractionType === "manual" && config.manualRules.length > 0) {
        const lastRule = config.manualRules[config.manualRules.length - 1];
        lastRule.selector = generatedSelector.value;
        lastRule.selectorType = "css";
        ElMessage.success("选择器已应用到最后一个规则");
      } else {
        // 自动切换到手动模式并添加规则
        config.extractionType = "manual";
        const newRule = {
          fieldName: "field_" + (config.manualRules.length + 1),
          selectorType: "css",
          selector: generatedSelector.value,
          attribute: "text",
          customAttribute: "",
          processing: ["trim"],
        };
        config.manualRules.push(newRule);
        ElMessage.success("已创建新的提取规则");
      }

      generatedSelector.value = "";
    };

    const validateConfig = () => {
      try {
        const configData = getConfigForPreview();

        // 基础验证
        if (!configData.url) {
          configValidation.value = {
            valid: false,
            message: "URL不能为空",
          };
          return;
        }

        if (!configData.extraction_rules) {
          configValidation.value = {
            valid: false,
            message: "提取规则不能为空",
          };
          return;
        }

        // 手动规则验证
        if (configData.extraction_rules.type === "manual") {
          const rules = configData.extraction_rules.rules || [];
          if (rules.length === 0) {
            configValidation.value = {
              valid: false,
              message: "手动模式至少需要一个提取规则",
            };
            return;
          }

          for (let i = 0; i < rules.length; i++) {
            const rule = rules[i];
            if (!rule.field_name || !rule.selector) {
              configValidation.value = {
                valid: false,
                message: `规则 ${i + 1} 缺少字段名称或选择器`,
              };
              return;
            }
          }
        }

        configValidation.value = {
          valid: true,
          message: "配置验证通过",
        };
      } catch (error) {
        configValidation.value = {
          valid: false,
          message: "配置格式错误: " + error.message,
        };
      }
    };

    const copyConfig = async () => {
      try {
        const configText = JSON.stringify(getConfigForPreview(), null, 2);
        await navigator.clipboard.writeText(configText);
        ElMessage.success("配置已复制到剪贴板");
      } catch (error) {
        console.error("复制失败:", error);
        ElMessage.error("复制失败");
      }
    };

    const exportConfig = () => {
      const configData = getConfigForPreview();
      const dataStr = JSON.stringify(configData, null, 2);
      const dataBlob = new Blob([dataStr], { type: "application/json" });

      const link = document.createElement("a");
      link.href = URL.createObjectURL(dataBlob);
      link.download = `extraction-config-${Date.now()}.json`;
      link.click();

      ElMessage.success("配置已导出");
    };

    const importConfig = (file) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedConfig = JSON.parse(e.target.result);

          // 验证导入的配置格式
          if (!importedConfig.url || !importedConfig.extraction_rules) {
            ElMessage.error("配置文件格式错误");
            return;
          }

          // 转换为组件内部格式
          config.name = importedConfig.name || "";
          config.url = importedConfig.url;

          if (importedConfig.extraction_rules.type === "auto") {
            config.extractionType = "auto";
            config.autoConfig = {
              elements: importedConfig.extraction_rules.elements || [
                "tables",
                "cards",
              ],
              confidenceThreshold:
                importedConfig.extraction_rules.confidence_threshold || 0.7,
            };
          } else {
            config.extractionType = "manual";
            config.manualRules = importedConfig.extraction_rules.rules.map(
              (rule) => ({
                fieldName: rule.field_name,
                selectorType: rule.selector_type,
                selector: rule.selector,
                attribute: rule.attribute,
                customAttribute:
                  rule.attribute === "custom" ? rule.customAttribute : "",
                processing: rule.processing || ["trim"],
              }),
            );
          }

          if (importedConfig.auth_config) {
            config.authEnabled = true;
            config.authConfig = {
              ...config.authConfig,
              ...importedConfig.auth_config,
            };
          }

          if (importedConfig.schedule_config) {
            config.scheduleEnabled = true;
            config.scheduleConfig = {
              ...config.scheduleConfig,
              ...importedConfig.schedule_config,
            };
          }

          if (importedConfig.wait_config) {
            config.waitConfig = {
              ...config.waitConfig,
              ...importedConfig.wait_config,
            };
          }

          ElMessage.success("配置导入成功");
        } catch (error) {
          console.error("导入配置失败:", error);
          ElMessage.error("配置文件格式错误");
        }
      };

      reader.readAsText(file.raw);
    };

    const clearTestResult = () => {
      testResult.value = null;
      testProgress.value = 0;
      testStatus.value = "";
      testMessage.value = "";
    };

    const getLogType = (level) => {
      switch (level.toLowerCase()) {
        case "error":
          return "danger";
        case "warning":
          return "warning";
        case "info":
          return "info";
        case "debug":
          return "info";
        default:
          return "info";
      }
    };

    // 监听来自iframe的消息
    window.addEventListener("message", (event) => {
      if (event.data.type === "element-selected") {
        generatedSelector.value = event.data.selector;
        ElMessage.success(`已选择元素: ${event.data.element.tagName}`);
      }
    });

    return {
      config,
      rules,
      configForm,
      saving,
      testing,
      previewTab,
      testResult,
      addRule,
      removeRule,
      getConfigForPreview,
      saveConfig,
      testExtraction,
      loadPagePreview,
      refreshPreview,
      toggleSelectorMode,
      onPreviewLoad,
      useGeneratedSelector,
      validateConfig,
      copyConfig,
      exportConfig,
      importConfig,
      clearTestResult,
      getLogType,
      previewIframe,
      loadingPreview,
      previewUrl,
      selectorMode,
      generatedSelector,
      configValidation,
      testProgress,
      testStatus,
      testMessage,
    };
  },
};
</script>

<style scoped>
.extraction-config {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rule-item {
  margin-bottom: 16px;
}

.rule-card {
  border: 1px solid #e4e7ed;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-rule-btn {
  width: 100%;
  margin-top: 16px;
  border: 2px dashed #d9d9d9;
  height: 40px;
}

.add-rule-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.input-suffix {
  margin-left: 8px;
  color: #909399;
}

.preview-card {
  margin-top: 20px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.page-preview-container {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.url-input {
  flex: 1;
  max-width: 60%;
}

.preview-controls {
  display: flex;
  gap: 8px;
}

.preview-content {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
}

.preview-placeholder p {
  margin: 16px 0 0 0;
  font-size: 14px;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.selector-panel {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
}

.generated-selector {
  margin-top: 12px;
}

.json-container {
  position: relative;
}

.json-toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.validation-result {
  margin-top: 16px;
}

.test-container {
  position: relative;
}

.test-toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.test-progress {
  margin: 16px 0;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.test-progress p {
  margin: 8px 0 0 0;
  color: #606266;
  font-size: 14px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.result-stats {
  display: flex;
  gap: 12px;
  align-items: center;
  font-size: 14px;
}

.result-count,
.result-time {
  color: #909399;
}

.result-data {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-family: "Courier New", monospace;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
  margin: 0;
}

.result-logs {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  font-size: 12px;
  color: #909399;
  font-family: monospace;
}

.log-message {
  flex: 1;
  font-size: 14px;
}

.result-errors {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.json-preview {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-family: "Courier New", monospace;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.test-result {
  margin-top: 16px;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
}

.test-result h4 {
  margin: 0 0 8px 0;
  color: #409eff;
}

.test-result pre {
  margin: 0;
  font-family: "Courier New", monospace;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
