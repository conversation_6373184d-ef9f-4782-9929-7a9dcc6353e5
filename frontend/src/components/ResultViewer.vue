<template>
  <div class="result-viewer">
    <div class="viewer-header">
      <h2>提取结果</h2>
      <div class="header-actions">
        <el-button @click="refreshResults" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-dropdown @command="handleExport" :disabled="!results.length">
          <el-button>
            <el-icon><Download /></el-icon>
            导出
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="json">导出为JSON</el-dropdown-item>
              <el-dropdown-item command="csv">导出为CSV</el-dropdown-item>
              <el-dropdown-item command="xlsx">导出为Excel</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="任务">
          <el-select v-model="filters.taskId" placeholder="选择任务" clearable>
            <el-option
              v-for="task in tasks"
              :key="task.id"
              :label="task.name"
              :value="task.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filters.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="数据状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable>
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="部分成功" value="partial" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="applyFilters">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 结果统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">总记录数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.success }}</div>
            <div class="stat-label">成功提取</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.failed }}</div>
            <div class="stat-label">提取失败</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ formatFileSize(stats.totalSize) }}</div>
            <div class="stat-label">数据大小</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 结果列表 -->
    <el-card class="results-card">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="results.length === 0" class="empty-state">
        <el-empty description="暂无提取结果" />
      </div>

      <div v-else>
        <!-- 视图切换 -->
        <div class="view-controls">
          <el-radio-group v-model="viewMode">
            <el-radio-button label="list">列表视图</el-radio-button>
            <el-radio-button label="table">表格视图</el-radio-button>
            <el-radio-button label="json">JSON视图</el-radio-button>
          </el-radio-group>
        </div>

        <!-- 列表视图 -->
        <div v-if="viewMode === 'list'" class="list-view">
          <div
            v-for="result in results"
            :key="result.id"
            class="result-item"
            @click="selectResult(result)"
          >
            <div class="result-header">
              <div class="result-info">
                <h4>{{ getTaskName(result.task_id) }}</h4>
                <span class="result-time">{{ formatTime(result.extracted_at) }}</span>
              </div>
              <div class="result-status">
                <el-tag
                  :type="getStatusType(result.status)"
                  size="small"
                >
                  {{ getStatusText(result.status) }}
                </el-tag>
              </div>
            </div>
            
            <div class="result-preview">
              <div class="data-summary">
                <span>提取字段: {{ getFieldCount(result.data) }}</span>
                <span>数据条数: {{ getRecordCount(result.data) }}</span>
              </div>
              
              <div class="data-sample">
                <pre>{{ getDataPreview(result.data) }}</pre>
              </div>
            </div>
          </div>
        </div>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'" class="table-view">
          <el-table
            :data="results"
            style="width: 100%"
            @row-click="selectResult"
            row-class-name="clickable-row"
          >
            <el-table-column prop="task_name" label="任务名称" width="200" />
            <el-table-column prop="extracted_at" label="提取时间" width="180">
              <template #default="{ row }">
                {{ formatTime(row.extracted_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="数据概览">
              <template #default="{ row }">
                <div class="data-overview">
                  <span>字段: {{ getFieldCount(row.data) }}</span>
                  <span>记录: {{ getRecordCount(row.data) }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button size="small" @click.stop="viewDetails(row)">
                  查看详情
                </el-button>
                <el-button size="small" @click.stop="downloadResult(row)">
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- JSON视图 -->
        <div v-if="viewMode === 'json'" class="json-view">
          <div class="json-controls">
            <el-select v-model="selectedResultId" placeholder="选择结果" @change="loadResultDetails">
              <el-option
                v-for="result in results"
                :key="result.id"
                :label="`${getTaskName(result.task_id)} - ${formatTime(result.extracted_at)}`"
                :value="result.id"
              />
            </el-select>
          </div>
          
          <div v-if="selectedResult" class="json-content">
            <pre class="json-display">{{ JSON.stringify(selectedResult.data, null, 2) }}</pre>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.limit"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="提取结果详情"
      width="80%"
      :before-close="closeDetailDialog"
    >
      <div v-if="detailResult" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务名称">
            {{ getTaskName(detailResult.task_id) }}
          </el-descriptions-item>
          <el-descriptions-item label="提取时间">
            {{ formatTime(detailResult.extracted_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="数据状态">
            <el-tag :type="getStatusType(detailResult.status)">
              {{ getStatusText(detailResult.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="数据哈希">
            {{ detailResult.data_hash }}
          </el-descriptions-item>
        </el-descriptions>

        <el-divider>提取数据</el-divider>
        
        <el-tabs v-model="detailTab">
          <el-tab-pane label="格式化视图" name="formatted">
            <div class="formatted-data">
              <div v-if="Array.isArray(detailResult.data)" class="data-table">
                <el-table :data="detailResult.data" style="width: 100%" max-height="400">
                  <el-table-column
                    v-for="(value, key) in getTableColumns(detailResult.data)"
                    :key="key"
                    :prop="key"
                    :label="key"
                    show-overflow-tooltip
                  />
                </el-table>
              </div>
              <div v-else class="data-object">
                <el-descriptions :column="1" border>
                  <el-descriptions-item
                    v-for="(value, key) in detailResult.data"
                    :key="key"
                    :label="key"
                  >
                    {{ formatValue(value) }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="JSON原始数据" name="raw">
            <pre class="json-raw">{{ JSON.stringify(detailResult.data, null, 2) }}</pre>
          </el-tab-pane>
        </el-tabs>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDetailDialog">关闭</el-button>
          <el-button type="primary" @click="downloadResult(detailResult)">
            下载数据
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Download, Search } from '@element-plus/icons-vue'
import api from '../utils/api'

export default {
  name: 'ResultViewer',
  components: {
    Refresh,
    Download,
    Search
  },
  setup() {
    const loading = ref(false)
    const results = ref([])
    const tasks = ref([])
    const viewMode = ref('list')
    const selectedResult = ref(null)
    const selectedResultId = ref('')
    const detailDialogVisible = ref(false)
    const detailResult = ref(null)
    const detailTab = ref('formatted')

    const filters = reactive({
      taskId: '',
      dateRange: [],
      status: ''
    })

    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0
    })

    const stats = computed(() => {
      const total = results.value.length
      const success = results.value.filter(r => r.status === 'success').length
      const failed = results.value.filter(r => r.status === 'failed').length
      const totalSize = results.value.reduce((sum, r) => {
        return sum + (r.data ? JSON.stringify(r.data).length : 0)
      }, 0)

      return { total, success, failed, totalSize }
    })

    const loadResults = async () => {
      try {
        loading.value = true
        const params = {
          page: pagination.page,
          limit: pagination.limit,
          ...filters
        }

        if (filters.dateRange && filters.dateRange.length === 2) {
          params.start_time = filters.dateRange[0]
          params.end_time = filters.dateRange[1]
        }

        const response = await api.get('/results/', { params })
        results.value = response.data.items
        pagination.total = response.data.total
      } catch (error) {
        console.error('加载结果失败:', error)
        ElMessage.error('加载结果失败')
      } finally {
        loading.value = false
      }
    }

    const loadTasks = async () => {
      try {
        const response = await api.get('/tasks/')
        tasks.value = response.data.items
      } catch (error) {
        console.error('加载任务列表失败:', error)
      }
    }

    const refreshResults = () => {
      loadResults()
    }

    const applyFilters = () => {
      pagination.page = 1
      loadResults()
    }

    const resetFilters = () => {
      Object.assign(filters, {
        taskId: '',
        dateRange: [],
        status: ''
      })
      pagination.page = 1
      loadResults()
    }

    const selectResult = (result) => {
      selectedResult.value = result
      selectedResultId.value = result.id
    }

    const viewDetails = (result) => {
      detailResult.value = result
      detailDialogVisible.value = true
    }

    const closeDetailDialog = () => {
      detailDialogVisible.value = false
      detailResult.value = null
      detailTab.value = 'formatted'
    }

    const loadResultDetails = async () => {
      if (!selectedResultId.value) return
      
      try {
        const response = await api.get(`/results/${selectedResultId.value}`)
        selectedResult.value = response.data
      } catch (error) {
        console.error('加载结果详情失败:', error)
        ElMessage.error('加载结果详情失败')
      }
    }

    const downloadResult = async (result) => {
      try {
        const data = JSON.stringify(result.data, null, 2)
        const blob = new Blob([data], { type: 'application/json' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `extraction_result_${result.id}.json`
        link.click()
        window.URL.revokeObjectURL(url)
        
        ElMessage.success('下载成功')
      } catch (error) {
        console.error('下载失败:', error)
        ElMessage.error('下载失败')
      }
    }

    const exportResults = async () => {
      try {
        const { value: format } = await ElMessageBox.prompt(
          '请选择导出格式',
          '导出数据',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType: 'select',
            inputOptions: {
              json: 'JSON',
              csv: 'CSV',
              excel: 'Excel'
            },
            inputValue: 'json'
          }
        )

        const response = await api.post('/results/export', {
          format,
          filters,
          result_ids: results.value.map(r => r.id)
        }, {
          responseType: 'blob'
        })

        const blob = new Blob([response.data])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `extraction_results.${format}`
        link.click()
        window.URL.revokeObjectURL(url)
        
        ElMessage.success('导出成功')
      } catch (error) {
        if (error !== 'cancel') {
          console.error('导出失败:', error)
          ElMessage.error('导出失败')
        }
      }
    }

    const handleSizeChange = (size) => {
      pagination.limit = size
      pagination.page = 1
      loadResults()
    }

    const handleCurrentChange = (page) => {
      pagination.page = page
      loadResults()
    }

    // 辅助函数
    const getTaskName = (taskId) => {
      const task = tasks.value.find(t => t.id === taskId)
      return task ? task.name : '未知任务'
    }

    const getStatusType = (status) => {
      const types = {
        success: 'success',
        failed: 'danger',
        partial: 'warning'
      }
      return types[status] || 'info'
    }

    const getStatusText = (status) => {
      const texts = {
        success: '成功',
        failed: '失败',
        partial: '部分成功'
      }
      return texts[status] || status
    }

    const formatTime = (time) => {
      return new Date(time).toLocaleString('zh-CN')
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const getFieldCount = (data) => {
      if (!data) return 0
      if (Array.isArray(data) && data.length > 0) {
        return Object.keys(data[0]).length
      }
      return Object.keys(data).length
    }

    const getRecordCount = (data) => {
      if (!data) return 0
      return Array.isArray(data) ? data.length : 1
    }

    const getDataPreview = (data) => {
      if (!data) return '无数据'
      const preview = JSON.stringify(data, null, 2)
      return preview.length > 200 ? preview.substring(0, 200) + '...' : preview
    }

    const getTableColumns = (data) => {
      if (!Array.isArray(data) || data.length === 0) return {}
      return data[0]
    }

    const formatValue = (value) => {
      if (typeof value === 'object') {
        return JSON.stringify(value)
      }
      return String(value)
    }

    onMounted(() => {
      loadResults()
      loadTasks()
    })

    return {
      loading,
      results,
      tasks,
      viewMode,
      selectedResult,
      selectedResultId,
      detailDialogVisible,
      detailResult,
      detailTab,
      filters,
      pagination,
      stats,
      refreshResults,
      applyFilters,
      resetFilters,
      selectResult,
      viewDetails,
      closeDetailDialog,
      loadResultDetails,
      downloadResult,
      exportResults,
      handleSizeChange,
      handleCurrentChange,
      getTaskName,
      getStatusType,
      getStatusText,
      formatTime,
      formatFileSize,
      getFieldCount,
      getRecordCount,
      getDataPreview,
      getTableColumns,
      formatValue
    }
  }
}
</script>

<style scoped>
.result-viewer {
  padding: 20px;
}

.viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.results-card {
  min-height: 400px;
}

.loading-container {
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.view-controls {
  margin-bottom: 20px;
  text-align: right;
}

/* 列表视图样式 */
.list-view {
  space-y: 16px;
}

.result-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.result-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
}

.result-time {
  color: #909399;
  font-size: 12px;
}

.result-preview {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
}

.data-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.data-sample {
  font-family: 'Courier New', monospace;
  font-size: 11px;
  color: #333;
  max-height: 100px;
  overflow: hidden;
}

/* 表格视图样式 */
.clickable-row {
  cursor: pointer;
}

.clickable-row:hover {
  background-color: #f5f7fa;
}

.data-overview {
  display: flex;
  gap: 10px;
  font-size: 12px;
  color: #666;
}

/* JSON视图样式 */
.json-view {
  padding: 20px;
}

.json-controls {
  margin-bottom: 20px;
}

.json-content {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
}

.json-display {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  margin: 0;
  max-height: 500px;
  overflow-y: auto;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

/* 详情弹窗样式 */
.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.formatted-data {
  margin-top: 16px;
}

.data-table {
  max-height: 400px;
  overflow-y: auto;
}

.data-object {
  max-height: 400px;
  overflow-y: auto;
}

.json-raw {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  margin: 0;
  max-height: 400px;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  gap: 10px;
}
</style>