import { defineStore } from 'pinia'
import api from '@/utils/api'

export const useTaskStore = defineStore('task', () => {
  
  const fetchTasks = async (params = {}) => {
    try {
      const response = await api.get('/tasks/', { params })
      return response.data
    } catch (error) {
      console.error('Failed to fetch tasks:', error)
      throw error
    }
  }

  const createTask = async (taskData) => {
    try {
      const response = await api.post('/tasks/', taskData)
      return response.data
    } catch (error) {
      console.error('Failed to create task:', error)
      throw error
    }
  }

  const updateTask = async (taskId, taskData) => {
    try {
      const response = await api.put(`/tasks/${taskId}/`, taskData)
      return response.data
    } catch (error) {
      console.error('Failed to update task:', error)
      throw error
    }
  }

  const deleteTask = async (taskId) => {
    try {
      await api.delete(`/tasks/${taskId}/`)
      return true
    } catch (error) {
      console.error('Failed to delete task:', error)
      throw error
    }
  }

  const executeTask = async (taskId) => {
    try {
      const response = await api.post(`/tasks/${taskId}/execute/`)
      return response.data
    } catch (error) {
      console.error('Failed to execute task:', error)
      throw error
    }
  }

  return {
    fetchTasks,
    createTask,
    updateTask,
    deleteTask,
    executeTask
  }
})
