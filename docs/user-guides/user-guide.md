# Loop Hole 系统使用指南

## 📋 目录

1. [系统概述](#系统概述)
2. [环境准备](#环境准备)
3. [获取和安装](#获取和安装)
4. [系统启动](#系统启动)
5. [使用教程](#使用教程)
6. [数据查看](#数据查看)
7. [日志监控](#日志监控)
8. [问题排查](#问题排查)
9. [高级功能](#高级功能)
10. [常见问题FAQ](#常见问题faq)

---

## 系统概述

Loop Hole 是一个智能的网页数据提取系统，能够自动分析复杂的管理后台网页，提取业务数据。主要特性：

- **智能页面分析**: 自动识别网页中的表格、卡片、列表等数据结构
- **多种提取模式**: 支持自动识别和手动配置两种模式
- **实时监控**: 提供任务状态监控和数据提取进度跟踪
- **多格式导出**: 支持JSON、CSV、Excel等格式导出
- **定时任务**: 支持定时自动执行数据提取任务

**技术架构**:
- 后端: Python + FastAPI + SQLAlchemy + Celery
- 前端: Vue.js 3 + Element Plus
- 数据库: PostgreSQL (开发环境可用SQLite)
- 缓存: Redis
- 容器化: Docker + Docker Compose

---

## 环境准备

### 最低系统要求

- **操作系统**: Windows 10+、macOS 10.15+、Linux (Ubuntu 18.04+)
- **内存**: 4GB RAM (推荐8GB+)
- **硬盘**: 5GB 可用空间
- **网络**: 能访问互联网

### 需要安装的软件

#### 选项1: Docker部署 (推荐)
```bash
# 只需要安装Docker即可
- Docker 20.0+
- Docker Compose 2.0+
```

#### 选项2: 源码部署
```bash
- Python 3.11+
- Node.js 18+
- PostgreSQL 13+ (可选，可用SQLite)
- Redis 6.0+
- Git
- uv (推荐) 或 pip (传统方式)
```

### 快速环境检查

运行以下命令检查环境：

```bash
# 检查Docker
docker --version
docker-compose --version

# 如果使用源码部署，检查以下
python --version
node --version
git --version
```

---

## 获取和安装

### 步骤 1: 克隆代码

```bash
# 克隆项目到本地
git clone <项目仓库地址>
cd loop_hole

# 查看项目结构
ls -la
```

你应该看到以下目录结构：
```
loop_hole/
├── app/                 # 后端代码
├── frontend/           # 前端代码
├── docs/               # 文档
├── docker/             # Docker配置
├── scripts/            # 脚本文件
├── tests/              # 测试文件
├── docker-compose.yml  # Docker编排文件
├── Dockerfile          # Docker镜像文件
├── pyproject.toml      # 项目配置和依赖 (推荐)
├── requirements.txt    # Python依赖 (兼容性)
├── uv.lock            # 依赖锁定文件
├── UV_USAGE.md        # uv使用指南
├── README.md           # 项目说明
└── ...
```

### 步骤 2: 配置环境变量

```bash
# 复制环境变量模板（如果存在）
cp .env.example .env

# 如果没有模板，创建.env文件
cat > .env << 'EOF'
# 数据库配置
DATABASE_URL=postgresql://loop_hole:password@localhost:5432/loop_hole
# 开发环境可以使用SQLite
# DATABASE_URL=sqlite:///./data/loop_hole.db

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 应用配置
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
DEBUG=True
LOG_LEVEL=info
ENVIRONMENT=development

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
EOF
```

---

## 系统启动

### 方法1: Docker启动 (推荐)

#### 开发环境启动
```bash
# 启动所有服务
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps

# 查看服务日志
docker-compose -f docker-compose.dev.yml logs -f
```

#### 生产环境启动
```bash
# 构建和启动
docker-compose up -d

# 查看状态
docker-compose ps
```

#### 验证启动成功
```bash
# 检查所有容器是否正在运行
docker ps

# 应该看到类似输出：
# CONTAINER ID   IMAGE                    STATUS
# xxxx           loop_hole_web           Up 2 minutes
# xxxx           loop_hole_worker        Up 2 minutes  
# xxxx           postgres:13             Up 2 minutes
# xxxx           redis:6-alpine          Up 2 minutes
```

### 方法2: 源码启动

#### 后端启动

```bash
# 1. 安装Python依赖 (推荐使用uv)
uv sync
# 或者使用传统方式
# pip install -r requirements.txt

# 2. 安装Playwright浏览器
playwright install

# 3. 初始化数据库
alembic upgrade head

# 4. 启动Redis (如果没有运行)
# macOS: brew services start redis
# Ubuntu: sudo systemctl start redis-server
# Windows: 下载Redis并启动

# 5. 启动后端API服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 6. 启动Celery工作进程 (新终端)
celery -A app.core.scheduler worker --loglevel=info

# 7. 启动Celery定时任务 (新终端，可选)
celery -A app.core.scheduler beat --loglevel=info
```

#### 前端启动

```bash
# 1. 进入前端目录
cd frontend

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev

# 前端将在 http://localhost:3000 启动
```

### 访问系统

启动成功后，你可以通过以下地址访问：

- **前端界面**: http://localhost (Docker) 或 http://localhost:3000 (源码)
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs (Swagger UI)
- **API文档(备用)**: http://localhost:8000/redoc

### 默认登录信息

系统提供以下默认用户账号：

#### 管理员账号
- **用户名**: `admin`
- **密码**: `admin123`
- **权限**: 管理员权限，可以访问所有功能

#### 演示账号
- **用户名**: `demo`
- **密码**: `demo123`
- **权限**: 普通用户权限

⚠️ **安全提醒**: 请在首次登录后立即修改默认密码！

### 用户管理

#### 创建管理员用户 (如果默认用户不存在)

如果系统启动后无法使用默认账号登录，可以手动创建管理员用户：

```bash
# 方法1: 使用创建用户脚本 (推荐)
python scripts/create_admin.py

# 方法2: 使用Docker环境
docker-compose exec app python scripts/create_admin.py
```

脚本会创建以下用户：
- 管理员用户: `admin` / `admin123`
- 演示用户: `demo` / `demo123`

#### 修改用户密码

⚠️ **注意**: 当前版本的前端界面暂未实现用户设置功能，点击"个人资料"会显示"功能开发中"。

**唯一可用方法: 数据库脚本重置**

⚠️ **重要说明**: 当前版本的后端API暂未实现密码修改端点，只能通过数据库脚本重置密码。
```bash
# 进入应用容器
docker-compose exec app bash

# 运行Python脚本重置密码
python -c "
from app.database import SessionLocal
from app.models.user import User
from app.api.v1.auth import hash_password

db = SessionLocal()
user = db.query(User).filter(User.username == 'admin').first()
if user:
    user.hashed_password = hash_password('new_password_here')
    db.commit()
    print('密码重置成功')
else:
    print('用户不存在')
db.close()
"
```

### 验证系统运行

```bash
# 测试后端API健康状态
curl http://localhost:8000/api/v1/health

# 应该返回类似：
# {"status": "healthy", "timestamp": "2025-01-15T..."}
```

---

## 使用教程

### 第一步: 创建数据提取任务

#### 1.1 打开系统界面
在浏览器中访问系统首页，你会看到主要的导航菜单。

#### 1.2 进入任务管理
点击 "任务管理" 或 "Tasks" 菜单项。

#### 1.3 创建新任务
点击 "创建新任务" 按钮，进入任务配置页面。

### 第二步: 配置数据提取规则

#### 2.1 基础配置
```
任务名称: 输入有意义的任务名称，如 "某某网站用户数据提取"
目标URL: 输入要分析的网页地址，如 https://example.com/admin/users
提取类型: 选择 "自动识别" 或 "手动配置"
```

#### 2.2 自动识别模式 (推荐新手)
```
识别元素: 勾选要提取的数据类型
  ☑️ 表格数据 - 识别HTML表格
  ☑️ 卡片数据 - 识别卡片式布局
  ☑️ 列表数据 - 识别列表结构

置信度阈值: 保持默认0.7即可
```

#### 2.3 手动配置模式 (高级用户)
如果网页结构复杂，可以手动配置提取规则：

```
字段名称: 如 "username", "email", "status"
选择器类型: CSS选择器 或 XPath
选择器: 如 ".user-table td:nth-child(1)" 
提取属性: 文本内容/HTML内容/属性值
数据处理: 勾选需要的数据清洗选项
```

**获取选择器的方法**:
1. 在Chrome中按F12打开开发者工具
2. 点击选择元素按钮(箭头图标)
3. 点击页面中要提取的元素
4. 右键选择的元素 → Copy → Copy selector

#### 2.4 高级配置
```
页面等待: 设置页面加载等待时间(毫秒)
等待元素: 指定等待特定元素加载完成
认证配置: 如果网页需要登录，配置认证信息
定时执行: 设置定期自动执行的频率
```

### 第三步: 测试配置

#### 3.1 配置预览
点击 "配置预览" 标签，查看生成的JSON配置是否正确。

#### 3.2 测试提取
1. 点击 "测试提取" 标签
2. 点击 "开始测试" 按钮
3. 等待测试完成，查看提取结果
4. 如果结果不符合预期，回到配置页面调整规则

#### 3.3 保存任务
测试满意后，点击 "保存配置" 按钮保存任务。

### 第四步: 执行任务

#### 4.1 手动执行
- 在任务列表中找到创建的任务
- 点击 "执行" 按钮立即运行
- 系统会显示任务执行状态

#### 4.2 查看执行进度
- 任务状态会实时更新: 排队中 → 执行中 → 完成/失败
- 点击任务名称可查看详细信息
- 可以随时停止正在执行的任务

---

## 数据查看

### 查看提取结果

#### 方法1: 通过界面查看
1. 点击 "结果查看" 或 "Results" 菜单
2. 选择任务和时间范围进行筛选
3. 点击结果条目查看详细数据
4. 支持表格、图表等多种展示方式

#### 方法2: 导出数据
1. 在结果页面选择要导出的数据
2. 点击 "导出" 按钮
3. 选择导出格式: JSON/CSV/Excel
4. 下载导出文件

#### 方法3: API直接获取
```bash
# 获取任务列表
curl "http://localhost:8000/api/v1/tasks"

# 获取特定任务的结果
curl "http://localhost:8000/api/v1/results?task_id=1"

# 下载JSON格式结果
curl "http://localhost:8000/api/v1/results/1/download?format=json" -o result.json
```

### 数据字段说明

提取结果包含以下字段：
```json
{
  "id": "结果ID",
  "task_id": "任务ID", 
  "url": "提取的网页URL",
  "status": "success/failed/partial",
  "data": {
    "field1": "提取的数据值1",
    "field2": "提取的数据值2"
  },
  "metadata": {
    "extraction_time": "提取耗时(毫秒)",
    "timestamp": "提取时间",
    "page_title": "页面标题"
  },
  "errors": ["错误信息数组"]
}
```

---

## 日志监控

### 查看系统日志

#### Docker环境
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f web      # 后端API日志
docker-compose logs -f worker   # 任务执行日志
docker-compose logs -f postgres # 数据库日志
docker-compose logs -f redis    # Redis日志

# 查看最近100行日志
docker-compose logs --tail=100 web
```

#### 源码环境
```bash
# 查看API服务日志 (运行uvicorn的终端)
# 日志会直接显示在终端中

# 查看Celery工作进程日志 (运行celery worker的终端)
# 日志会直接显示在终端中

# 查看文件日志 (如果配置了文件日志)
tail -f logs/app.log
tail -f logs/celery.log
```

### 日志级别说明

```
DEBUG: 调试信息，包含详细的执行过程
INFO:  一般信息，正常操作日志  
WARN:  警告信息，可能的问题
ERROR: 错误信息，需要关注的问题
```

### 重要日志位置

- **API请求日志**: 记录所有HTTP请求和响应
- **任务执行日志**: 记录数据提取任务的详细过程
- **错误日志**: 记录系统错误和异常
- **性能日志**: 记录系统性能指标

### 通过界面查看日志

1. 进入 "系统监控" 或 "Dashboard" 页面
2. 查看 "系统日志" 部分
3. 支持按时间、级别、服务筛选日志
4. 可以搜索特定关键词

---

## 问题排查

### 常见启动问题

#### 问题1: Docker容器启动失败
```bash
# 检查错误原因
docker-compose logs

# 常见原因和解决方案:
# 1. 端口被占用 -> 修改docker-compose.yml中的端口映射
# 2. 内存不足 -> 关闭其他应用释放内存
# 3. 权限问题 -> 使用sudo运行docker命令
```

#### 问题2: 数据库连接失败
```bash
# 检查数据库容器状态
docker-compose ps postgres

# 检查数据库日志
docker-compose logs postgres

# 解决方案:
# 1. 检查DATABASE_URL配置是否正确
# 2. 确保PostgreSQL容器正常运行
# 3. 检查数据库用户名密码
```

#### 问题3: Redis连接失败
```bash
# 检查Redis容器状态  
docker-compose ps redis

# 测试Redis连接
docker-compose exec redis redis-cli ping
# 应该返回 PONG

# 解决方案:
# 1. 检查REDIS_URL配置
# 2. 确保Redis容器正常运行
# 3. 检查Redis密码配置
```

#### 问题4: 前端无法访问
```bash
# 检查nginx或前端容器状态
docker-compose ps web

# 检查端口映射
docker-compose port web 80

# 解决方案:
# 1. 确保端口80未被占用
# 2. 检查防火墙设置
# 3. 尝试使用IP地址访问
```

### 常见使用问题

#### 问题1: 登录失败 - 用户名或密码错误
**现象**: 使用默认账号无法登录，提示"Invalid username or password"
**排查步骤**:
1. 确认使用正确的默认账号：
   - 管理员: `admin` / `admin123`
   - 演示用户: `demo` / `demo123`
2. 检查数据库中是否存在用户
3. 重新创建管理员用户

**解决方案**:
```bash
# 检查数据库中的用户
# SQLite环境
sqlite3 data/loop_hole.db "SELECT username, email, role, is_active FROM users;"

# PostgreSQL环境
docker-compose exec postgres psql -U postgres -d loop_hole -c "SELECT username, email, role, is_active FROM users;"

# 如果没有用户，创建管理员用户
python scripts/create_admin.py
```

#### 问题2: 403 Forbidden 错误
**现象**: 登录后访问API接口返回403错误
**可能原因**:
1. JWT令牌过期或无效
2. 用户权限不足
3. 前端未正确发送认证头

**解决方案**:
```bash
# 检查JWT配置
echo $SECRET_KEY
echo $JWT_SECRET_KEY

# 重新登录获取新令牌
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

#### 问题3: 页面无法加载
**现象**: 测试提取时报告页面加载失败
**排查步骤**:
1. 检查URL是否正确可访问
2. 检查网络连接
3. 检查目标网站是否有反爬虫限制
4. 增加页面等待时间
5. 配置User-Agent和代理

**解决方案**:
```bash
# 手动测试URL是否可访问
curl -I "https://target-website.com"

# 检查Playwright浏览器安装
playwright install --help
```

#### 问题2: 数据提取不准确
**现象**: 提取结果为空或不正确
**排查步骤**:
1. 使用浏览器开发者工具检查页面结构
2. 验证CSS选择器是否正确
3. 检查页面是否需要JavaScript渲染
4. 增加等待时间让页面完全加载

**调试方法**:
```javascript
// 在浏览器控制台测试选择器
document.querySelectorAll('你的CSS选择器')

// 检查元素是否存在
document.querySelector('你的CSS选择器')
```

#### 问题3: 任务执行很慢
**可能原因**:
1. 页面加载时间长
2. 网络连接慢
3. 服务器资源不足
4. 并发任务过多

**优化方案**:
```bash
# 查看系统资源使用
docker stats

# 调整Celery并发数
# 在docker-compose.yml中设置:
# environment:
#   - CELERY_WORKER_CONCURRENCY=2
```

#### 问题4: 认证失败
**现象**: 需要登录的页面无法访问
**解决方案**:
1. 确认认证配置正确
2. 检查登录表单字段名称
3. 使用Cookie认证方式
4. 手动获取登录后的Cookie

**获取Cookie方法**:
1. 在浏览器中手动登录
2. 按F12打开开发者工具
3. 在Network标签中找到请求
4. 复制Cookie字符串到配置中

### 性能问题排查

#### 内存使用过高
```bash
# 查看容器内存使用
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# 减少Celery并发数
# 或增加系统内存
```

#### CPU使用过高
```bash
# 查看进程CPU使用
top -p $(pgrep -f celery)

# 优化措施:
# 1. 减少并发任务数
# 2. 优化提取规则
# 3. 增加任务间隔时间
```

### 获取技术支持

#### 收集诊断信息
```bash
# 生成系统信息报告
cat > debug_info.txt << 'EOF'
=== 系统信息 ===
$(uname -a)
$(docker --version)
$(docker-compose --version)

=== 容器状态 ===
$(docker-compose ps)

=== 最新日志 ===
$(docker-compose logs --tail=50)
EOF
```

#### 联系支持
提供以下信息会帮助更快解决问题:
1. 操作系统和版本
2. Docker版本信息
3. 错误信息和日志
4. 重现问题的步骤
5. 系统配置文件

---

## 高级功能

### 批量任务管理

#### 批量创建任务
```bash
# 通过API批量创建任务
curl -X POST "http://localhost:8000/api/v1/tasks/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "tasks": [
      {
        "name": "任务1",
        "url": "https://example1.com",
        "extraction_rules": {...}
      },
      {
        "name": "任务2", 
        "url": "https://example2.com",
        "extraction_rules": {...}
      }
    ]
  }'
```

#### 批量执行任务
```bash
# 批量执行指定任务
curl -X POST "http://localhost:8000/api/v1/tasks/execute-batch" \
  -H "Content-Type: application/json" \
  -d '{"task_ids": [1, 2, 3]}'
```

### 定时任务设置

#### Cron表达式说明
```bash
# 格式: 分钟 小时 日 月 星期
"0 */2 * * *"     # 每2小时执行一次
"0 9 * * 1-5"     # 工作日上午9点执行
"0 0 1 * *"       # 每月1号执行
"*/15 * * * *"    # 每15分钟执行
```

#### 定时任务监控
```bash
# 查看定时任务状态
curl "http://localhost:8000/api/v1/tasks/schedule/status"

# 手动触发定时任务
curl -X POST "http://localhost:8000/api/v1/tasks/schedule/trigger/1"
```

### 数据处理管道

#### 自定义数据清洗
系统支持多种数据处理选项:
- `trim`: 去除首尾空白
- `lowercase`: 转换为小写
- `uppercase`: 转换为大写
- `removeHtml`: 移除HTML标签
- `extractNumbers`: 提取数字
- `extractDates`: 提取日期

#### 数据验证规则
```json
{
  "validation_rules": {
    "required_fields": ["name", "email"],
    "data_types": {
      "email": "email",
      "age": "integer",
      "price": "float"
    },
    "patterns": {
      "phone": "^\\d{11}$"
    }
  }
}
```

### API集成

#### Webhook通知
```json
{
  "webhook_config": {
    "url": "https://your-server.com/webhook",
    "events": ["task_completed", "task_failed"],
    "headers": {
      "Authorization": "Bearer your-token"
    }
  }
}
```

#### 第三方系统集成
系统提供完整的REST API，可以轻松集成到现有系统中:

```python
# Python集成示例
import requests

# 创建任务
task_data = {
    "name": "集成测试任务",
    "url": "https://example.com",
    "extraction_rules": {...}
}
response = requests.post("http://localhost:8000/api/v1/tasks", json=task_data)
task_id = response.json()["id"]

# 执行任务
requests.post(f"http://localhost:8000/api/v1/tasks/{task_id}/execute")

# 获取结果
results = requests.get(f"http://localhost:8000/api/v1/results?task_id={task_id}")
```

---

## 常见问题FAQ

### Q1: 默认用户名和密码是什么？
**A**: 系统提供以下默认账号：
- **管理员**: 用户名 `admin`，密码 `admin123`
- **演示用户**: 用户名 `demo`，密码 `demo123`

如果无法登录，请运行 `python scripts/create_admin.py` 创建用户。

### Q2: 如何修改密码？
**A**: 当前版本只能通过数据库脚本重置密码：

```bash
# 进入应用容器重置密码
docker-compose exec app python -c "
from app.database import SessionLocal
from app.models.user import User
from app.api.v1.auth import hash_password
db = SessionLocal()
user = db.query(User).filter(User.username == 'admin').first()
user.hashed_password = hash_password('your_new_password')
db.commit()
print('密码重置成功')
"
```

⚠️ **注意**: 前端用户设置功能和后端密码修改API都暂未实现。

### Q3: 忘记密码怎么办？
**A**: 管理员可以通过以下方式重置密码：
```bash
# 进入应用容器
docker-compose exec app python -c "
from app.database import SessionLocal
from app.models.user import User
from app.api.v1.auth import hash_password
db = SessionLocal()
user = db.query(User).filter(User.username == 'admin').first()
user.hashed_password = hash_password('new_password')
db.commit()
print('密码重置成功')
"
```

### Q4: 如何创建新用户？
**A**: 可以通过以下方式创建用户：
1. **API注册**: 使用 `/api/v1/auth/register` 端点
2. **管理员创建**: 在前端管理界面创建
3. **脚本创建**: 修改 `scripts/create_admin.py` 脚本

### Q5: 系统支持哪些类型的网页？
**A**: 支持大多数现代网页，包括:
- 静态HTML页面
- JavaScript渲染的单页应用(SPA)
- 需要登录认证的管理后台
- AJAX动态加载的内容
- 表格、卡片、列表等结构化数据

### Q2: 能处理多少并发任务？
**A**: 默认配置支持4-8个并发任务，可以根据服务器性能调整:
```bash
# 修改docker-compose.yml
environment:
  - CELERY_WORKER_CONCURRENCY=10  # 根据CPU核心数调整
```

### Q3: 提取的数据如何保存？
**A**: 数据默认保存在PostgreSQL数据库中，支持多种导出格式:
- JSON: API直接返回
- CSV: 适合Excel打开
- Excel: .xlsx格式，支持多个工作表
- 数据库查询: 直接SQL查询

### Q4: 如何处理反爬虫机制？
**A**: 系统提供多种反反爬虫策略:
- 随机User-Agent
- 代理IP轮换
- 请求频率控制
- JavaScript渲染等待
- Cookie和Session管理

### Q5: 系统安全性如何？
**A**: 系统包含多项安全措施:
- JWT令牌认证
- API访问频率限制
- 敏感数据加密存储
- CORS跨域保护
- SQL注入防护

### Q6: 能否处理大型网站？
**A**: 可以，但需要合理配置:
- 增加页面等待时间
- 分批处理数据
- 使用代理避免IP封禁
- 合理设置请求间隔

### Q7: 如何处理动态加载的内容？
**A**: 系统使用Playwright浏览器自动化:
- 等待JavaScript执行完成
- 监听网络请求完成
- 等待特定元素出现
- 模拟用户交互(点击、滚动)

### Q8: 系统资源需求如何？
**A**: 建议配置:
- **最小**: 2核CPU, 4GB内存
- **推荐**: 4核CPU, 8GB内存
- **高负载**: 8核CPU, 16GB内存

### Q9: 支持哪些数据格式？
**A**: 输出格式:
- JSON (默认)
- CSV (表格数据)
- Excel (.xlsx)
- XML (可定制)
- 自定义格式

### Q10: 如何备份和恢复数据？
**A**: 
```bash
# 数据库备份
docker-compose exec postgres pg_dump -U loop_hole loop_hole > backup.sql

# 恢复数据
docker-compose exec postgres psql -U loop_hole loop_hole < backup.sql

# 完整系统备份
docker-compose down
tar czf loop_hole_backup.tar.gz .
```

---

## 总结

本指南涵盖了Loop Hole系统的完整使用流程，从环境准备到高级功能使用。建议新用户按以下顺序操作:

1. **环境准备** → 安装必要软件
2. **系统启动** → 选择Docker或源码方式启动  
3. **基础使用** → 创建第一个数据提取任务
4. **结果查看** → 导出和分析提取的数据
5. **问题排查** → 遇到问题时的排查方法
6. **高级功能** → 批量操作和API集成

如果遇到问题，请优先查看 [问题排查](#问题排查) 部分，收集相关日志信息有助于快速解决问题。

**快速开始命令总结**:
```bash
# Docker方式 (推荐)
git clone <项目地址>
cd loop_hole
docker-compose up -d

# 访问 http://localhost 开始使用

# 源码方式
git clone <项目地址>
cd loop_hole
uv sync  # 或 pip install -r requirements.txt
playwright install
uvicorn app.main:app --reload &
cd frontend && npm install && npm run dev
```

祝使用愉快！🚀