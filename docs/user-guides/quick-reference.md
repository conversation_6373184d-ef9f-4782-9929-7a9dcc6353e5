# 提取规则和调度配置快速参考

## 🚀 快速开始模板

### 基础表格提取
```json
{
  "table_data": {
    "type": "table",
    "selector": "#data-table",
    "fields": {
      "列名1": "td:nth-child(1)",
      "列名2": "td:nth-child(2)",
      "列名3": "td:nth-child(3)"
    }
  }
}
```

### 卡片数据提取
```json
{
  "card_data": {
    "type": "css",
    "selector": ".card",
    "fields": {
      "标题": ".title",
      "内容": ".content",
      "链接": "a@href",
      "图片": "img@src"
    }
  }
}
```

### 自动智能提取
```json
{
  "auto_data": {
    "type": "auto",
    "elements": ["tables", "cards"],
    "confidence_threshold": 0.7
  }
}
```

## ⏰ 常用调度配置

### 每天执行
```json
{
  "type": "cron",
  "expression": "0 9 * * *",
  "timezone": "Asia/Shanghai"
}
```

### 工作日执行
```json
{
  "type": "cron", 
  "expression": "0 9 * * 1-5",
  "timezone": "Asia/Shanghai"
}
```

### 每小时执行
```json
{
  "type": "interval",
  "hours": 1
}
```

### 每30分钟执行
```json
{
  "type": "interval",
  "minutes": 30
}
```

## 🔧 常用CSS选择器

| 选择器 | 说明 | 示例 |
|--------|------|------|
| `.class` | 类选择器 | `.user-name` |
| `#id` | ID选择器 | `#user-table` |
| `tag` | 标签选择器 | `div`, `span`, `a` |
| `.parent .child` | 后代选择器 | `.card .title` |
| `.parent > .child` | 直接子元素 | `.row > .col` |
| `:nth-child(n)` | 第n个子元素 | `td:nth-child(2)` |
| `:first-child` | 第一个子元素 | `tr:first-child` |
| `:last-child` | 最后一个子元素 | `td:last-child` |
| `[attr]` | 有属性的元素 | `[data-id]` |
| `[attr="value"]` | 属性值匹配 | `[class="active"]` |

## 🔐 认证配置模板

### Cookie认证
```json
{
  "type": "cookie",
  "credentials": {
    "sessionid": "你的session值",
    "csrftoken": "你的csrf值"
  }
}
```

### 表单登录
```json
{
  "type": "form",
  "login_url": "https://example.com/login",
  "credentials": {
    "username": "用户名",
    "password": "密码"
  }
}
```

## ⏱️ Cron表达式速查

| 表达式 | 说明 |
|--------|------|
| `0 0 * * *` | 每天午夜 |
| `0 9 * * *` | 每天上午9点 |
| `0 9 * * 1-5` | 工作日上午9点 |
| `0 */2 * * *` | 每2小时 |
| `*/15 * * * *` | 每15分钟 |
| `0 0 1 * *` | 每月1号 |
| `0 0 * * 0` | 每周日 |
| `0 9,17 * * *` | 每天9点和17点 |

## 🛠️ 调试技巧

### 1. 测试CSS选择器
在浏览器控制台运行：
```javascript
document.querySelectorAll('你的选择器')
```

### 2. 获取元素属性
```javascript
var el = document.querySelector('选择器');
console.log(el.textContent);  // 文本内容
console.log(el.getAttribute('href')); // 属性值
```

### 3. 获取Cookie
1. F12 → Network → 刷新页面
2. 点击任意请求 → Headers → Request Headers → Cookie

## ❗ 常见问题解决

| 问题 | 解决方案 |
|------|----------|
| 提取数据为空 | 检查选择器是否正确 |
| 页面加载超时 | 增加timeout时间 |
| 认证失败 | 更新Cookie或登录信息 |
| 数据不完整 | 增加页面等待时间 |

## 📝 完整任务示例

```json
{
  "name": "用户数据提取",
  "url": "https://admin.example.com/users",
  "extraction_rules": {
    "users": {
      "type": "table",
      "selector": "#user-table",
      "fields": {
        "id": "td:nth-child(1)",
        "name": "td:nth-child(2)",
        "email": "td:nth-child(3)",
        "status": "td:nth-child(4) .badge"
      }
    }
  },
  "schedule_config": {
    "type": "cron",
    "expression": "0 9 * * 1-5",
    "timezone": "Asia/Shanghai"
  },
  "auth_config": {
    "type": "cookie",
    "credentials": {
      "sessionid": "abc123..."
    }
  }
}
```

## 🔗 相关文档

- [详细指南](./extraction-rules-guide.md) - 完整的配置说明
- [用户手册](./user-guide.md) - 系统使用指南
- [API文档](./api-documentation.md) - API接口说明

---

💡 **提示**: 建议先用简单的配置测试，确认可以正常提取数据后再添加复杂的调度和认证配置。
