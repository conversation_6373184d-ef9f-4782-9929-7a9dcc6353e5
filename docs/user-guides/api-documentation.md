# Loop Hole - API 文档

## API 概览

Loop Hole 提供 RESTful API 接口，支持完整的数据提取任务管理和执行功能。

**Base URL**: `https://api.loophole.example.com/api/v1`

**认证方式**: Bearer <PERSON> (JWT)

**内容类型**: `application/json`

## 认证

### 获取访问令牌

```http
POST /auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

**响应示例**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 86400,
  "user": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "username": "your_username",
    "role": "admin"
  }
}
```

### 使用令牌

所有API请求都需要在请求头中包含访问令牌：

```http
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## 任务管理 API

### 创建提取任务

```http
POST /tasks
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "销售数据提取",
  "url": "https://admin.example.com/sales-dashboard",
  "extraction_rules": {
    "tables": [
      {
        "selector": "#sales-table",
        "name": "sales_data",
        "columns": ["date", "product", "amount", "region"]
      }
    ],
    "cards": [
      {
        "selector": ".metric-card",
        "name": "kpi_metrics",
        "fields": ["title", "value", "change"]
      }
    ]
  },
  "schedule_config": {
    "type": "cron",
    "expression": "0 9 * * *",
    "timezone": "Asia/Shanghai"
  },
  "auth_config": {
    "type": "cookie",
    "credentials": {
      "session_id": "abc123...",
      "csrf_token": "xyz789..."
    }
  }
}
```

**响应示例**:
```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "销售数据提取",
  "status": "created",
  "created_at": "2024-01-15T10:30:00Z",
  "next_run": "2024-01-16T09:00:00Z"
}
```

### 获取任务列表

```http
GET /tasks?page=1&limit=20&status=active
Authorization: Bearer {token}
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)
- `status`: 任务状态 (`active`, `paused`, `completed`, `failed`)
- `search`: 搜索关键词

**响应示例**:
```json
{
  "tasks": [
    {
      "task_id": "550e8400-e29b-41d4-a716-446655440000",
      "name": "销售数据提取",
      "url": "https://admin.example.com/sales-dashboard",
      "status": "active",
      "last_run": "2024-01-15T09:00:00Z",
      "next_run": "2024-01-16T09:00:00Z",
      "success_rate": 0.95,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 45,
    "pages": 3
  }
}
```

### 获取任务详情

```http
GET /tasks/{task_id}
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "销售数据提取",
  "url": "https://admin.example.com/sales-dashboard",
  "extraction_rules": {
    "tables": [
      {
        "selector": "#sales-table",
        "name": "sales_data",
        "columns": ["date", "product", "amount", "region"]
      }
    ]
  },
  "schedule_config": {
    "type": "cron",
    "expression": "0 9 * * *",
    "timezone": "Asia/Shanghai"
  },
  "status": "active",
  "statistics": {
    "total_runs": 30,
    "successful_runs": 28,
    "failed_runs": 2,
    "success_rate": 0.93,
    "avg_duration": 12.5,
    "last_error": null
  },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### 更新任务

```http
PUT /tasks/{task_id}
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "销售数据提取 - 更新版",
  "schedule_config": {
    "type": "cron",
    "expression": "0 8,20 * * *",
    "timezone": "Asia/Shanghai"
  }
}
```

### 删除任务

```http
DELETE /tasks/{task_id}
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "message": "Task deleted successfully",
  "task_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

## 任务执行 API

### 立即执行任务

```http
POST /tasks/{task_id}/execute
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "job_id": "job_123456789",
  "status": "queued",
  "estimated_duration": 15,
  "queue_position": 3
}
```

### 获取执行状态

```http
GET /jobs/{job_id}
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "job_id": "job_123456789",
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "running",
  "progress": 0.6,
  "started_at": "2024-01-15T14:30:00Z",
  "estimated_completion": "2024-01-15T14:32:00Z",
  "logs": [
    {
      "timestamp": "2024-01-15T14:30:05Z",
      "level": "info",
      "message": "页面加载完成"
    },
    {
      "timestamp": "2024-01-15T14:30:10Z",
      "level": "info",
      "message": "开始数据提取"
    }
  ]
}
```

## 数据提取 API

### 页面分析

```http
POST /extract/analyze
Content-Type: application/json
Authorization: Bearer {token}

{
  "url": "https://admin.example.com/dashboard",
  "auth_config": {
    "type": "cookie",
    "credentials": {
      "session_id": "abc123..."
    }
  },
  "options": {
    "wait_for_content": true,
    "timeout": 30000,
    "viewport": {
      "width": 1920,
      "height": 1080
    }
  }
}
```

**响应示例**:
```json
{
  "analysis_id": "analysis_789",
  "url": "https://admin.example.com/dashboard",
  "detected_elements": {
    "tables": [
      {
        "selector": "#data-table",
        "headers": ["日期", "产品", "销量", "收入"],
        "row_count": 25,
        "confidence": 0.95
      }
    ],
    "cards": [
      {
        "selector": ".metric-card",
        "fields": ["标题", "数值", "变化率"],
        "count": 4,
        "confidence": 0.88
      }
    ],
    "charts": [
      {
        "selector": "#revenue-chart",
        "type": "line",
        "data_points": 12,
        "confidence": 0.72
      }
    ]
  },
  "suggested_rules": {
    "tables": [
      {
        "selector": "#data-table",
        "name": "sales_data",
        "columns": ["date", "product", "sales", "revenue"]
      }
    ]
  },
  "screenshot_url": "https://storage.example.com/screenshots/analysis_789.png"
}
```

### 执行数据提取

```http
POST /extract/execute
Content-Type: application/json
Authorization: Bearer {token}

{
  "url": "https://admin.example.com/dashboard",
  "extraction_rules": {
    "tables": [
      {
        "selector": "#data-table",
        "name": "sales_data",
        "columns": ["date", "product", "sales", "revenue"]
      }
    ]
  },
  "auth_config": {
    "type": "cookie",
    "credentials": {
      "session_id": "abc123..."
    }
  }
}
```

**响应示例**:
```json
{
  "extraction_id": "ext_456789",
  "status": "completed",
  "extracted_data": {
    "sales_data": [
      {
        "date": "2024-01-15",
        "product": "产品A",
        "sales": 150,
        "revenue": 15000
      },
      {
        "date": "2024-01-15",
        "product": "产品B",
        "sales": 200,
        "revenue": 25000
      }
    ]
  },
  "metadata": {
    "extraction_time": "2024-01-15T14:35:00Z",
    "duration": 8.5,
    "total_records": 25,
    "data_hash": "sha256:abc123..."
  }
}
```

## 结果查询 API

### 获取提取结果

```http
GET /results?task_id={task_id}&page=1&limit=10
Authorization: Bearer {token}
```

**查询参数**:
- `task_id`: 任务ID (可选)
- `start_date`: 开始日期 (ISO 8601格式)
- `end_date`: 结束日期 (ISO 8601格式)
- `format`: 返回格式 (`json`, `csv`, `excel`)

**响应示例**:
```json
{
  "results": [
    {
      "result_id": "result_123",
      "task_id": "550e8400-e29b-41d4-a716-446655440000",
      "extracted_at": "2024-01-15T14:35:00Z",
      "data_summary": {
        "tables": {
          "sales_data": 25
        },
        "cards": {
          "kpi_metrics": 4
        }
      },
      "download_url": "https://api.example.com/results/result_123/download"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 150
  }
}
```

### 下载提取结果

```http
GET /results/{result_id}/download?format=csv
Authorization: Bearer {token}
```

**支持格式**:
- `json`: JSON格式
- `csv`: CSV格式
- `excel`: Excel格式

## 配置管理 API

### 获取提取模板

```http
GET /templates
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "templates": [
    {
      "template_id": "tpl_001",
      "name": "电商销售数据模板",
      "description": "适用于电商平台销售数据提取",
      "extraction_rules": {
        "tables": [
          {
            "selector": ".sales-table",
            "name": "sales_data",
            "columns": ["date", "product", "sales", "revenue"]
          }
        ]
      },
      "created_at": "2024-01-10T10:00:00Z"
    }
  ]
}
```

### 创建提取模板

```http
POST /templates
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "自定义数据模板",
  "description": "用于特定页面的数据提取",
  "extraction_rules": {
    "tables": [
      {
        "selector": "#custom-table",
        "name": "custom_data",
        "columns": ["field1", "field2", "field3"]
      }
    ]
  }
}
```

## Webhook API

### 配置 Webhook

```http
POST /webhooks
Content-Type: application/json
Authorization: Bearer {token}

{
  "url": "https://your-app.com/webhook/loop-hole",
  "events": ["task.completed", "task.failed", "extraction.completed"],
  "secret": "your_webhook_secret",
  "active": true
}
```

### Webhook 事件格式

当任务完成时，系统会向配置的URL发送POST请求：

```json
{
  "event": "task.completed",
  "timestamp": "2024-01-15T14:35:00Z",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "job_id": "job_123456789",
    "status": "completed",
    "duration": 12.5,
    "extracted_records": 25,
    "result_id": "result_123"
  }
}
```

## SDK 使用示例

### Python SDK

```python
from loop_hole_sdk import LoopHoleClient

# 初始化客户端
client = LoopHoleClient(
    base_url="https://api.loophole.example.com",
    api_key="your_api_key"
)

# 创建任务
task = client.tasks.create(
    name="销售数据提取",
    url="https://admin.example.com/dashboard",
    extraction_rules={
        "tables": [{
            "selector": "#sales-table",
            "name": "sales_data",
            "columns": ["date", "product", "amount"]
        }]
    },
    schedule="0 9 * * *"
)

print(f"任务创建成功: {task.id}")

# 执行任务
job = client.tasks.execute(task.id)
print(f"任务执行中: {job.id}")

# 等待完成
result = client.jobs.wait_for_completion(job.id)
print(f"提取到 {len(result.data)} 条记录")

# 获取数据
for record in result.data:
    print(record)
```

### JavaScript SDK

```javascript
import { LoopHoleClient } from '@loophole/sdk';

// 初始化客户端
const client = new LoopHoleClient({
  baseURL: 'https://api.loophole.example.com',
  apiKey: 'your_api_key'
});

// 创建任务
const task = await client.tasks.create({
  name: '销售数据提取',
  url: 'https://admin.example.com/dashboard',
  extractionRules: {
    tables: [{
      selector: '#sales-table',
      name: 'sales_data',
      columns: ['date', 'product', 'amount']
    }]
  },
  schedule: '0 9 * * *'
});

console.log(`任务创建成功: ${task.id}`);

// 执行任务
const job = await client.tasks.execute(task.id);
console.log(`任务执行中: ${job.id}`);

// 监听完成事件
job.on('completed', (result) => {
  console.log(`提取到 ${result.data.length} 条记录`);
  result.data.forEach(record => console.log(record));
});
```

## 错误处理

### 错误响应格式

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": [
      {
        "field": "url",
        "message": "URL格式不正确"
      }
    ]
  },
  "request_id": "req_123456789"
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| `INVALID_TOKEN` | 401 | 访问令牌无效或已过期 |
| `INSUFFICIENT_PERMISSIONS` | 403 | 权限不足 |
| `RESOURCE_NOT_FOUND` | 404 | 资源不存在 |
| `VALIDATION_ERROR` | 400 | 请求参数验证失败 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |
| `EXTRACTION_FAILED` | 422 | 数据提取失败 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |

## 速率限制

API 请求受到速率限制保护：

- **认证用户**: 1000 请求/小时
- **管理员用户**: 5000 请求/小时
- **企业用户**: 10000 请求/小时

超出限制时，响应头会包含限制信息：

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
```

## 最佳实践

### 1. 认证管理
- 定期轮换API密钥
- 使用环境变量存储敏感信息
- 实现令牌自动刷新机制

### 2. 错误处理
- 实现指数退避重试策略
- 记录详细的错误日志
- 监控API调用成功率

### 3. 性能优化
- 使用分页获取大量数据
- 缓存不经常变化的数据
- 批量操作减少API调用次数

### 4. 数据安全
- 使用HTTPS传输数据
- 验证Webhook签名
- 定期审计API访问日志
