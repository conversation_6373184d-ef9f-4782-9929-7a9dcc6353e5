# 实际配置示例

本文档提供了一些真实场景的配置示例，可以直接复制使用或作为参考。

## 📊 电商管理后台示例

### 商品列表提取

**场景**: 提取电商后台的商品列表数据

**提取规则**:
```json
{
  "products": {
    "type": "table",
    "selector": "#product-table",
    "fields": {
      "商品ID": "td:nth-child(1)",
      "商品名称": "td:nth-child(2) a",
      "价格": "td:nth-child(3) .price",
      "库存": "td:nth-child(4)",
      "分类": "td:nth-child(5)",
      "状态": "td:nth-child(6) .status-badge",
      "详情链接": "td:nth-child(2) a@href"
    }
  }
}
```

**调度配置** (每4小时执行一次):
```json
{
  "type": "cron",
  "expression": "0 */4 * * *",
  "timezone": "Asia/Shanghai"
}
```

### 订单数据提取

**场景**: 提取订单管理页面的数据

**提取规则**:
```json
{
  "orders": {
    "type": "table", 
    "selector": ".order-table",
    "fields": {
      "订单号": "td:nth-child(1) .order-id",
      "客户姓名": "td:nth-child(2)",
      "订单金额": "td:nth-child(3) .amount",
      "订单状态": "td:nth-child(4) .status",
      "下单时间": "td:nth-child(5)",
      "操作": "td:nth-child(6) .actions"
    }
  }
}
```

**调度配置** (工作时间每小时执行):
```json
{
  "type": "cron",
  "expression": "0 9-18 * * 1-5",
  "timezone": "Asia/Shanghai"
}
```

## 👥 用户管理系统示例

### 用户卡片数据

**场景**: 提取用户管理页面的卡片式用户信息

**提取规则**:
```json
{
  "users": {
    "type": "css",
    "selector": ".user-card",
    "fields": {
      "用户ID": "@data-user-id",
      "用户名": ".username",
      "邮箱": ".email",
      "手机号": ".phone",
      "注册时间": ".register-date",
      "最后登录": ".last-login",
      "状态": ".status-indicator@class",
      "头像": ".avatar img@src"
    }
  }
}
```

**调度配置** (每天早上9点执行):
```json
{
  "type": "cron",
  "expression": "0 9 * * *",
  "timezone": "Asia/Shanghai"
}
```

## 📈 数据仪表板示例

### KPI指标提取

**场景**: 提取仪表板页面的关键指标

**提取规则**:
```json
{
  "kpi_metrics": {
    "type": "css",
    "selector": ".metric-card",
    "fields": {
      "指标名称": ".metric-title",
      "当前值": ".metric-value",
      "变化率": ".metric-change",
      "趋势": ".trend-arrow@class",
      "更新时间": ".last-updated"
    }
  },
  "chart_data": {
    "type": "css",
    "selector": ".chart-container",
    "fields": {
      "图表标题": ".chart-title",
      "数据源": "@data-source",
      "图表类型": "@data-chart-type"
    }
  }
}
```

**调度配置** (每30分钟执行):
```json
{
  "type": "interval",
  "minutes": 30
}
```

## 🏢 CRM系统示例

### 客户信息提取

**场景**: 提取CRM系统的客户列表

**提取规则**:
```json
{
  "customers": {
    "type": "table",
    "selector": "#customer-table",
    "fields": {
      "客户ID": "td:nth-child(1)",
      "公司名称": "td:nth-child(2) .company-name",
      "联系人": "td:nth-child(3)",
      "联系电话": "td:nth-child(4)",
      "邮箱": "td:nth-child(5)",
      "行业": "td:nth-child(6)",
      "客户等级": "td:nth-child(7) .level-badge",
      "最后联系": "td:nth-child(8)"
    }
  }
}
```

**认证配置** (Cookie认证):
```json
{
  "type": "cookie",
  "credentials": {
    "sessionid": "your_session_id_here",
    "csrftoken": "your_csrf_token_here"
  }
}
```

**调度配置** (工作日上午和下午各执行一次):
```json
{
  "type": "cron",
  "expression": "0 9,17 * * 1-5",
  "timezone": "Asia/Shanghai"
}
```

## 📰 内容管理示例

### 文章列表提取

**场景**: 提取内容管理系统的文章列表

**提取规则**:
```json
{
  "articles": {
    "type": "css",
    "selector": ".article-item",
    "fields": {
      "文章ID": "@data-article-id",
      "标题": ".article-title a",
      "作者": ".author-name",
      "发布时间": ".publish-date",
      "分类": ".category-tag",
      "状态": ".status-label",
      "阅读量": ".view-count",
      "文章链接": ".article-title a@href",
      "封面图": ".article-thumb img@src"
    }
  }
}
```

**调度配置** (每2小时执行):
```json
{
  "type": "cron",
  "expression": "0 */2 * * *",
  "timezone": "Asia/Shanghai"
}
```

## 🎯 自动识别示例

### 智能数据提取

**场景**: 让系统自动分析页面并提取所有结构化数据

**提取规则**:
```json
{
  "auto_extraction": {
    "type": "auto",
    "elements": ["tables", "cards", "lists"],
    "confidence_threshold": 0.8
  }
}
```

**调度配置** (每天执行一次):
```json
{
  "type": "cron",
  "expression": "0 2 * * *",
  "timezone": "Asia/Shanghai"
}
```

## 🔧 复杂场景示例

### 多页面数据聚合

**场景**: 需要从多个相关页面提取数据

**提取规则**:
```json
{
  "main_data": {
    "type": "table",
    "selector": "#main-table",
    "fields": {
      "ID": "td:nth-child(1)",
      "名称": "td:nth-child(2)",
      "详情链接": "td:nth-child(2) a@href"
    }
  },
  "pagination": {
    "type": "css",
    "selector": ".pagination",
    "fields": {
      "当前页": ".current-page",
      "总页数": ".total-pages",
      "下一页链接": ".next-page@href"
    }
  }
}
```

### 动态加载内容

**场景**: 页面内容通过JavaScript动态加载

**提取规则**:
```json
{
  "dynamic_content": {
    "type": "css",
    "selector": ".dynamic-item",
    "fields": {
      "标题": ".item-title",
      "内容": ".item-content",
      "时间": ".item-time"
    }
  }
}
```

**等待配置**:
```json
{
  "wait_config": {
    "timeout": 20000,
    "wait_for_selector": ".dynamic-item:nth-child(5)",
    "delay": 3000
  }
}
```

## 💡 使用建议

1. **从简单开始**: 先用基本的表格或CSS选择器提取，确认可以正常工作
2. **逐步完善**: 然后添加认证、调度等高级配置
3. **测试验证**: 每次修改配置后都要测试提取效果
4. **监控运行**: 定期检查任务执行状态和数据质量

## 🔗 相关文档

- [详细配置指南](./extraction-rules-guide.md)
- [快速参考手册](./quick-reference.md)
- [用户使用指南](./user-guide.md)
