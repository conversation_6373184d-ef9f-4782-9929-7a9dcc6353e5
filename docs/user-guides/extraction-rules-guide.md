# 数据提取规则和调度配置详细指南

## 📋 目录

1. [概述](#概述)
2. [提取规则详解](#提取规则详解)
3. [调度配置详解](#调度配置详解)
4. [完整示例](#完整示例)
5. [常见问题](#常见问题)

---

## 概述

在 Loop Hole 系统中创建任务时，需要配置两个核心部分：
- **提取规则 (extraction_rules)**: 定义如何从网页中提取数据
- **调度配置 (schedule_config)**: 定义任务的执行时间和频率（可选）

这两个配置都使用 JSON 格式，本文档将详细说明各种配置选项和用法。

---

## 提取规则详解

提取规则支持四种主要类型：`css`、`xpath`、`table`、`auto`。

### 1. CSS 选择器提取 (type: "css")

最常用的提取方式，使用 CSS 选择器定位元素。

#### 基本格式
```json
{
  "规则名称": {
    "type": "css",
    "selector": "CSS选择器",
    "fields": {
      "字段名1": "子选择器1",
      "字段名2": "子选择器2"
    }
  }
}
```

#### 详细示例
```json
{
  "user_data": {
    "type": "css",
    "selector": ".user-card",
    "fields": {
      "name": ".user-name",
      "email": ".user-email", 
      "status": ".status-badge",
      "avatar": "@src",
      "profile_url": "a@href"
    }
  }
}
```

#### 字段提取说明
- **文本内容**: 直接使用选择器，如 `".user-name"`
- **属性值**: 使用 `@属性名` 格式，如 `"@href"`、`"@src"`、`"@data-id"`
- **子元素**: 使用相对选择器，如 `"a.profile-link"`

#### 常用 CSS 选择器
```css
/* 基础选择器 */
.class-name          /* 类选择器 */
#element-id          /* ID选择器 */
tag-name             /* 标签选择器 */

/* 组合选择器 */
.parent .child       /* 后代选择器 */
.parent > .child     /* 直接子元素 */
.element:nth-child(2) /* 第2个子元素 */
.element:first-child  /* 第一个子元素 */
.element:last-child   /* 最后一个子元素 */

/* 属性选择器 */
[data-id]            /* 有data-id属性的元素 */
[data-id="123"]      /* data-id等于123的元素 */
[class*="user"]      /* class包含user的元素 */
```

### 2. XPath 提取 (type: "xpath")

适用于复杂的元素定位需求。

#### 基本格式
```json
{
  "规则名称": {
    "type": "xpath",
    "xpath": "XPath表达式"
  }
}
```

#### 示例
```json
{
  "product_prices": {
    "type": "xpath", 
    "xpath": "//div[@class='product']//span[contains(@class, 'price')]/text()"
  }
}
```

#### 常用 XPath 表达式
```xpath
//div[@class='content']           # 任意位置的class为content的div
//table//tr[position()>1]         # 表格中除第一行外的所有行
//a[contains(@href, 'product')]   # href包含product的链接
//span[text()='价格']/following-sibling::span  # 文本为"价格"的span的下一个兄弟span
```

### 3. 表格提取 (type: "table")

专门用于提取 HTML 表格数据。

#### 基本格式
```json
{
  "规则名称": {
    "type": "table",
    "selector": "表格选择器",
    "fields": {
      "列名1": "td:nth-child(1)",
      "列名2": "td:nth-child(2)"
    }
  }
}
```

#### 详细示例
```json
{
  "sales_data": {
    "type": "table",
    "selector": "#sales-table",
    "fields": {
      "date": "td:nth-child(1)",
      "product": "td:nth-child(2)", 
      "amount": "td:nth-child(3)",
      "region": "td:nth-child(4)"
    }
  }
}
```

#### 表格提取特点
- 自动识别表头 (`<th>` 或第一行 `<td>`)
- 自动提取所有数据行
- 支持复杂表格结构
- 可指定特定列的提取规则

### 4. 自动分析提取 (type: "auto")

系统智能分析页面结构，自动识别数据。

#### 基本格式
```json
{
  "auto_extraction": {
    "type": "auto",
    "elements": ["tables", "cards", "lists"],
    "confidence_threshold": 0.7
  }
}
```

#### 配置选项
- **elements**: 要识别的元素类型
  - `"tables"`: 表格数据
  - `"cards"`: 卡片式布局
  - `"lists"`: 列表数据
- **confidence_threshold**: 置信度阈值 (0.0-1.0)

---

## 调度配置详解

调度配置定义任务的执行时间和频率，支持多种调度方式。

### 1. Cron 表达式调度

最灵活的调度方式，使用标准 Cron 表达式。

#### 基本格式
```json
{
  "type": "cron",
  "expression": "分钟 小时 日 月 星期",
  "timezone": "时区"
}
```

#### 详细示例
```json
{
  "type": "cron",
  "expression": "0 9 * * 1-5",
  "timezone": "Asia/Shanghai"
}
```

#### Cron 表达式说明
```bash
# 格式: 分钟(0-59) 小时(0-23) 日(1-31) 月(1-12) 星期(0-7)

# 常用示例
"0 0 * * *"      # 每天午夜执行
"0 9 * * 1-5"    # 工作日上午9点执行  
"0 */2 * * *"    # 每2小时执行一次
"*/15 * * * *"   # 每15分钟执行一次
"0 0 1 * *"      # 每月1号执行
"0 0 * * 0"      # 每周日执行
"0 9,17 * * *"   # 每天9点和17点执行
```

#### 支持的时区
```
Asia/Shanghai    # 中国标准时间
UTC             # 协调世界时
America/New_York # 美国东部时间
Europe/London   # 英国时间
Asia/Tokyo      # 日本时间
```

### 2. 间隔调度

按固定时间间隔执行任务。

#### 基本格式
```json
{
  "type": "interval",
  "seconds": 3600,
  "start_date": "2024-01-01T00:00:00Z"
}
```

#### 配置选项
- **seconds**: 间隔秒数
- **minutes**: 间隔分钟数 (可选)
- **hours**: 间隔小时数 (可选)
- **start_date**: 开始时间 (可选)

#### 示例
```json
{
  "type": "interval",
  "minutes": 30,
  "start_date": "2024-01-15T09:00:00Z"
}
```

### 3. 一次性调度

在指定时间执行一次。

#### 基本格式
```json
{
  "type": "date",
  "run_date": "2024-01-15T14:30:00Z"
}
```

---

## 完整示例

### 示例1: 电商产品数据提取

```json
{
  "name": "电商产品数据提取",
  "url": "https://shop.example.com/admin/products",
  "extraction_rules": {
    "products": {
      "type": "table",
      "selector": "#product-table",
      "fields": {
        "name": "td:nth-child(1) a",
        "price": "td:nth-child(2) .price",
        "stock": "td:nth-child(3)",
        "category": "td:nth-child(4)",
        "status": "td:nth-child(5) .status-badge"
      }
    }
  },
  "schedule_config": {
    "type": "cron",
    "expression": "0 */4 * * *",
    "timezone": "Asia/Shanghai"
  }
}
```

### 示例2: 用户数据卡片提取

```json
{
  "name": "用户数据卡片提取", 
  "url": "https://admin.example.com/users",
  "extraction_rules": {
    "users": {
      "type": "css",
      "selector": ".user-card",
      "fields": {
        "username": ".username",
        "email": ".email",
        "last_login": ".last-login",
        "avatar": "img@src",
        "profile_link": "a.profile@href"
      }
    }
  },
  "schedule_config": {
    "type": "interval",
    "hours": 6
  }
}
```

### 示例3: 自动分析提取

```json
{
  "name": "智能数据提取",
  "url": "https://dashboard.example.com",
  "extraction_rules": {
    "auto_data": {
      "type": "auto",
      "elements": ["tables", "cards"],
      "confidence_threshold": 0.8
    }
  },
  "schedule_config": {
    "type": "cron",
    "expression": "0 9 * * 1-5",
    "timezone": "Asia/Shanghai"
  }
}
```

---

## 常见问题

### Q1: 如何获取正确的 CSS 选择器？

**方法1: 浏览器开发者工具**
1. 按 F12 打开开发者工具
2. 点击选择元素按钮 (箭头图标)
3. 点击页面中要提取的元素
4. 右键选中的元素 → Copy → Copy selector

**方法2: 手动构建**
```css
/* 根据元素特征构建 */
.class-name              /* 类名 */
#element-id             /* ID */
[data-attribute="value"] /* 属性 */
```

### Q2: 提取的数据为空怎么办？

**排查步骤:**
1. 检查选择器是否正确
2. 确认页面是否需要 JavaScript 渲染
3. 增加页面等待时间
4. 检查元素是否在 iframe 中

### Q3: Cron 表达式如何测试？

**在线工具:**
- https://crontab.guru/
- https://cron-expression-descriptor.azurewebsites.net/

**常用表达式:**
```bash
"0 0 * * *"     # 每天午夜
"0 9-17 * * *"  # 工作时间每小时
"*/30 * * * *"  # 每30分钟
```

### Q4: 如何处理动态加载的内容？

**解决方案:**
1. 增加等待时间
2. 等待特定元素出现
3. 使用 XPath 定位动态元素

### Q5: 支持哪些时区？

系统支持标准时区标识符，常用的包括：
- `Asia/Shanghai` (北京时间)
- `UTC` (协调世界时)
- `America/New_York` (美国东部)
- `Europe/London` (英国时间)

---

## 高级配置选项

### 1. 认证配置 (auth_config)

当目标网页需要登录时，可以配置认证信息。

#### Cookie 认证 (推荐)
```json
{
  "type": "cookie",
  "credentials": {
    "session_id": "abc123...",
    "csrf_token": "xyz789...",
    "user_token": "token123..."
  }
}
```

**获取 Cookie 的方法:**
1. 在浏览器中手动登录目标网站
2. 按 F12 打开开发者工具
3. 进入 Network 标签页
4. 刷新页面，查看请求头中的 Cookie
5. 复制需要的 Cookie 值

#### 表单登录认证
```json
{
  "type": "form",
  "login_url": "https://example.com/login",
  "credentials": {
    "username": "your_username",
    "password": "your_password"
  },
  "form_fields": {
    "username_field": "username",
    "password_field": "password"
  }
}
```

#### HTTP Basic 认证
```json
{
  "type": "basic",
  "credentials": {
    "username": "your_username",
    "password": "your_password"
  }
}
```

### 2. 页面等待配置 (wait_config)

控制页面加载和等待行为。

```json
{
  "wait_config": {
    "timeout": 30000,
    "wait_for_selector": ".data-loaded",
    "wait_for_network": true,
    "delay": 2000
  }
}
```

**配置选项:**
- **timeout**: 页面加载超时时间 (毫秒)
- **wait_for_selector**: 等待特定元素出现
- **wait_for_network**: 等待网络请求完成
- **delay**: 额外等待时间 (毫秒)

### 3. 数据处理配置

#### 数据清洗选项
```json
{
  "processing": {
    "trim": true,
    "remove_html": true,
    "lowercase": false,
    "extract_numbers": false,
    "date_format": "YYYY-MM-DD"
  }
}
```

**处理选项说明:**
- **trim**: 去除首尾空白字符
- **remove_html**: 移除 HTML 标签
- **lowercase**: 转换为小写
- **uppercase**: 转换为大写
- **extract_numbers**: 只提取数字
- **extract_dates**: 提取日期格式

#### 数据验证规则
```json
{
  "validation": {
    "required_fields": ["name", "email"],
    "data_types": {
      "email": "email",
      "age": "integer",
      "price": "float",
      "date": "date"
    },
    "patterns": {
      "phone": "^\\d{11}$",
      "id_card": "^\\d{18}$"
    }
  }
}
```

## 复杂场景示例

### 示例1: 需要登录的管理后台

```json
{
  "name": "CRM用户数据提取",
  "url": "https://crm.company.com/users",
  "extraction_rules": {
    "users": {
      "type": "table",
      "selector": "#user-table",
      "fields": {
        "id": "td:nth-child(1)",
        "name": "td:nth-child(2) a",
        "email": "td:nth-child(3)",
        "phone": "td:nth-child(4)",
        "status": "td:nth-child(5) .badge",
        "last_login": "td:nth-child(6)"
      }
    }
  },
  "auth_config": {
    "type": "cookie",
    "credentials": {
      "sessionid": "abc123def456...",
      "csrftoken": "xyz789uvw012..."
    }
  },
  "wait_config": {
    "timeout": 15000,
    "wait_for_selector": "#user-table tbody tr",
    "delay": 1000
  },
  "schedule_config": {
    "type": "cron",
    "expression": "0 8,12,18 * * 1-5",
    "timezone": "Asia/Shanghai"
  }
}
```

### 示例2: 动态加载的数据面板

```json
{
  "name": "销售仪表板数据",
  "url": "https://dashboard.company.com/sales",
  "extraction_rules": {
    "metrics": {
      "type": "css",
      "selector": ".metric-card",
      "fields": {
        "title": ".metric-title",
        "value": ".metric-value",
        "change": ".metric-change",
        "trend": ".trend-indicator@class"
      }
    },
    "chart_data": {
      "type": "css",
      "selector": ".chart-container",
      "fields": {
        "chart_type": "@data-chart-type",
        "data_source": "@data-source",
        "last_updated": ".last-updated"
      }
    }
  },
  "wait_config": {
    "timeout": 20000,
    "wait_for_selector": ".metric-card:nth-child(4)",
    "wait_for_network": true,
    "delay": 3000
  },
  "schedule_config": {
    "type": "interval",
    "minutes": 30
  }
}
```

### 示例3: 多页面数据聚合

```json
{
  "name": "电商订单多页面提取",
  "url": "https://admin.shop.com/orders",
  "extraction_rules": {
    "orders": {
      "type": "table",
      "selector": "#orders-table",
      "fields": {
        "order_id": "td:nth-child(1) a",
        "customer": "td:nth-child(2)",
        "amount": "td:nth-child(3) .amount",
        "status": "td:nth-child(4) .status",
        "date": "td:nth-child(5)",
        "detail_link": "td:nth-child(1) a@href"
      }
    },
    "pagination": {
      "type": "css",
      "selector": ".pagination",
      "fields": {
        "current_page": ".current",
        "total_pages": ".page-info",
        "next_link": ".next@href"
      }
    }
  },
  "processing": {
    "trim": true,
    "remove_html": true
  },
  "validation": {
    "required_fields": ["order_id", "amount"],
    "data_types": {
      "amount": "float",
      "date": "date"
    }
  },
  "schedule_config": {
    "type": "cron",
    "expression": "0 */2 * * *",
    "timezone": "Asia/Shanghai"
  }
}
```

## 调试和优化技巧

### 1. 调试提取规则

**步骤1: 浏览器测试**
```javascript
// 在浏览器控制台测试选择器
document.querySelectorAll('你的CSS选择器');

// 测试XPath
document.evaluate('你的XPath', document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
```

**步骤2: 检查元素属性**
```javascript
// 查看元素的所有属性
var element = document.querySelector('你的选择器');
console.log(element.attributes);
console.log(element.textContent);
```

### 2. 性能优化建议

**选择器优化:**
- 使用具体的选择器，避免过于宽泛
- 优先使用 ID 选择器 (`#id`)
- 避免深层嵌套选择器

**等待时间优化:**
- 根据页面实际加载时间调整 timeout
- 使用 `wait_for_selector` 替代固定延迟
- 合理设置 `delay` 时间

**调度频率优化:**
- 根据数据更新频率设置合理的执行间隔
- 避免过于频繁的执行影响目标网站
- 考虑目标网站的访问高峰期

### 3. 错误处理

**常见错误及解决方案:**

| 错误类型 | 可能原因 | 解决方案 |
|---------|---------|---------|
| 元素未找到 | 选择器错误 | 检查并修正选择器 |
| 页面加载超时 | 网络慢或页面复杂 | 增加 timeout 时间 |
| 认证失败 | Cookie 过期 | 更新认证信息 |
| 数据格式错误 | 页面结构变化 | 更新提取规则 |

## 总结

本指南涵盖了 Loop Hole 系统中提取规则和调度配置的所有主要功能。建议：

1. **新手用户**: 从 CSS 选择器和简单 Cron 表达式开始
2. **进阶用户**: 尝试 XPath 和复杂调度配置
3. **测试优先**: 创建任务后先手动测试，确认提取效果
4. **逐步优化**: 根据实际需求调整配置参数
5. **监控运行**: 定期检查任务执行状态和数据质量

如有疑问，请参考系统日志或联系技术支持。
