#!/bin/bash

# 文档重组整理脚本
# 使用方法: chmod +x reorganize_docs.sh && ./reorganize_docs.sh

set -e  # 遇到错误立即退出

echo "🚀 开始文档重组整理..."

# 检查是否在正确的目录
if [ ! -d "docs" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 创建新目录结构
echo "📁 创建新目录结构..."
mkdir -p docs/user-guides
mkdir -p docs/technical-guides
mkdir -p docs/reports
mkdir -p docs/archive

# 移动用户指南类文档
echo "👥 移动用户指南类文档..."
if [ -f "docs/guide/user_guide.md" ]; then
    mv "docs/guide/user_guide.md" "docs/user-guides/user-guide.md"
    echo "  ✅ 移动 user_guide.md"
fi

if [ -f "docs/api_documentation.md" ]; then
    mv "docs/api_documentation.md" "docs/user-guides/api-documentation.md"
    echo "  ✅ 移动 api_documentation.md"
fi

# 移动技术指南类文档
echo "🔧 移动技术指南类文档..."
if [ -f "docs/technical_architecture.md" ]; then
    mv "docs/technical_architecture.md" "docs/technical-guides/architecture.md"
    echo "  ✅ 移动 technical_architecture.md"
fi

if [ -f "docs/environment_setup.md" ]; then
    mv "docs/environment_setup.md" "docs/technical-guides/environment-setup.md"
    echo "  ✅ 移动 environment_setup.md"
fi

if [ -f "docs/maintenance_operations.md" ]; then
    mv "docs/maintenance_operations.md" "docs/technical-guides/maintenance.md"
    echo "  ✅ 移动 maintenance_operations.md"
fi

if [ -f "docs/guide/PLAYWRIGHT_CONFIGURATION_GUIDE.md" ]; then
    mv "docs/guide/PLAYWRIGHT_CONFIGURATION_GUIDE.md" "docs/technical-guides/playwright-config.md"
    echo "  ✅ 移动 PLAYWRIGHT_CONFIGURATION_GUIDE.md"
fi

if [ -f "docs/guide/authentication_troubleshooting_guide.md" ]; then
    mv "docs/guide/authentication_troubleshooting_guide.md" "docs/technical-guides/auth-troubleshooting.md"
    echo "  ✅ 移动 authentication_troubleshooting_guide.md"
fi

if [ -f "docs/guide/database_selection_guide.md" ]; then
    mv "docs/guide/database_selection_guide.md" "docs/technical-guides/database-guide.md"
    echo "  ✅ 移动 database_selection_guide.md"
fi

if [ -f "docs/guide/implementation_guide.md" ]; then
    mv "docs/guide/implementation_guide.md" "docs/technical-guides/implementation.md"
    echo "  ✅ 移动 implementation_guide.md"
fi

# 移动报告类文档
echo "📊 移动报告类文档..."
if [ -f "docs/improvements/IMPROVEMENT_ROADMAP.md" ]; then
    mv "docs/improvements/IMPROVEMENT_ROADMAP.md" "docs/reports/improvement-roadmap.md"
    echo "  ✅ 移动 IMPROVEMENT_ROADMAP.md"
fi

if [ -f "docs/product/product.md" ]; then
    mv "docs/product/product.md" "docs/reports/product-specification.md"
    echo "  ✅ 移动 product.md"
fi

# 移动待删除文档到归档目录
echo "🗄️ 移动待删除文档到归档目录..."

# 无关文档
if [ -f "docs/ai_prompt.md" ]; then
    mv "docs/ai_prompt.md" "docs/archive/"
    echo "  📦 归档 ai_prompt.md"
fi

if [ -f "docs/大模型API优惠活动汇总.md" ]; then
    mv "docs/大模型API优惠活动汇总.md" "docs/archive/"
    echo "  📦 归档 大模型API优惠活动汇总.md"
fi

# 状态报告文档
reports_to_archive=(
    "FINAL_TESTING_REPORT.md"
    "FIXES_APPLIED.md"
    "GITHUB_TRENDING_TEST_REPORT.md"
    "HIGH_PRIORITY_IMPROVEMENTS_COMPLETED.md"
    "MEMORY_USAGE_REPORT.md"
    "PROJECT_COMPLETION_ASSESSMENT.md"
    "PROJECT_STATUS_SUMMARY.md"
    "NEXT_STEPS_ROADMAP.md"
)

for report in "${reports_to_archive[@]}"; do
    if [ -f "docs/$report" ]; then
        mv "docs/$report" "docs/archive/"
        echo "  📦 归档 $report"
    fi
done

# 重复的改进文档
improvement_docs=(
    "improvements/Project_Improvement_Plan.md"
    "improvements/Project_Improvement_Plan_Translated_zh_CN.md"
    "improvements/项目改进建议.md"
)

for doc in "${improvement_docs[@]}"; do
    if [ -f "docs/$doc" ]; then
        mv "docs/$doc" "docs/archive/"
        echo "  📦 归档 $doc"
    fi
done

# Git清理指南
if [ -f "docs/guide/git_history_cleanup_guide.md" ]; then
    mv "docs/guide/git_history_cleanup_guide.md" "docs/archive/"
    echo "  📦 归档 git_history_cleanup_guide.md"
fi

# 清理空目录
echo "🧹 清理空目录..."
if [ -d "docs/guide" ] && [ -z "$(ls -A docs/guide)" ]; then
    rmdir "docs/guide"
    echo "  🗑️ 删除空目录 docs/guide"
fi

if [ -d "docs/improvements" ] && [ -z "$(ls -A docs/improvements)" ]; then
    rmdir "docs/improvements"
    echo "  🗑️ 删除空目录 docs/improvements"
fi

if [ -d "docs/product" ] && [ -z "$(ls -A docs/product)" ]; then
    rmdir "docs/product"
    echo "  🗑️ 删除空目录 docs/product"
fi

# 创建各目录的README文件
echo "📝 创建目录索引文件..."

# 用户指南目录README
cat > docs/user-guides/README.md << 'EOF'
# 用户指南

本目录包含面向最终用户的使用指南和文档。

## 📚 文档列表

- [**用户使用指南**](./user-guide.md) - 完整的系统使用教程
- [**API接口文档**](./api-documentation.md) - REST API接口说明

## 🎯 适用人群

- 系统最终用户
- API集成开发者
- 产品使用者

## 📞 获取帮助

如果在使用过程中遇到问题，请查看：
1. [用户指南](./user-guide.md)中的常见问题部分
2. [技术指南](../technical-guides/)中的故障排除文档
3. 提交Issue或联系技术支持
EOF

# 技术指南目录README
cat > docs/technical-guides/README.md << 'EOF'
# 技术指南

本目录包含面向开发者和运维人员的技术实现指南。

## 📚 文档列表

- [**系统架构**](./architecture.md) - 整体架构设计说明
- [**环境搭建**](./environment-setup.md) - 开发和部署环境配置
- [**运维操作**](./maintenance.md) - 系统维护和运维指南
- [**Playwright配置**](./playwright-config.md) - 浏览器自动化配置
- [**认证问题排查**](./auth-troubleshooting.md) - 认证相关问题解决
- [**数据库指南**](./database-guide.md) - 数据库选择和配置
- [**实现细节**](./implementation.md) - 核心功能实现说明

## 🎯 适用人群

- 后端开发者
- 前端开发者
- 系统运维人员
- DevOps工程师

## 🔧 快速导航

- **新手开发者**: 建议先阅读[环境搭建](./environment-setup.md)和[系统架构](./architecture.md)
- **运维人员**: 重点关注[运维操作](./maintenance.md)和相关配置文档
- **问题排查**: 查看对应的故障排除指南
EOF

# 报告目录README
cat > docs/reports/README.md << 'EOF'
# 项目报告

本目录包含项目相关的报告、规划和规格文档。

## 📚 文档列表

- [**改进路线图**](./improvement-roadmap.md) - 项目改进计划和路线图
- [**产品规格说明**](./product-specification.md) - 产品功能规格和说明

## 🎯 适用人群

- 项目管理者
- 产品经理
- 技术负责人
- 投资者和决策者

## 📊 文档类型

- **规划类**: 未来发展方向和改进计划
- **规格类**: 产品功能和技术规格
- **分析类**: 项目状态和质量分析
EOF

echo "✅ 文档重组完成！"
echo ""
echo "📁 新的目录结构："
echo "docs/"
echo "├── user-guides/          # 用户使用指南"
echo "├── technical-guides/     # 技术实现指南"
echo "├── development-plans/    # 开发计划（已存在）"
echo "├── reports/              # 项目报告和规划"
echo "└── archive/              # 归档文档（待删除）"
echo ""
echo "🗑️ 归档的文档位于 docs/archive/ 目录"
echo "   请确认后可以删除整个 archive 目录"
echo ""
echo "📝 下一步："
echo "1. 检查新的目录结构是否符合预期"
echo "2. 更新主README.md中的文档链接"
echo "3. 确认归档文档可以删除后，执行: rm -rf docs/archive"
echo ""
echo "🎉 文档整理完成！"
