# 认证问题排查指南

## 🔍 问题概述

在Loop Hole系统中遇到的403 Forbidden错误主要由以下原因引起：

1. **前端未发送认证令牌**：用户未登录或令牌已过期
2. **API端点错误**：某些端点存在代码错误导致500错误
3. **认证流程配置问题**：JWT配置或用户数据问题

## 🛠️ 已修复的问题

### 1. ExtractionResult模型字段错误

**问题**：`/api/v1/results/` 端点返回500错误
```python
# 错误代码
query = query.order_by(ExtractionResult.created_at.desc())
# AttributeError: type object 'ExtractionResult' has no attribute 'created_at'
```

**解决方案**：修正字段名为 `extracted_at`
```python
# 修复后代码
query = query.order_by(ExtractionResult.extracted_at.desc())
```

## 🔐 认证系统状态

### 当前认证配置

- **JWT算法**: HS256
- **令牌有效期**: 24小时
- **密钥来源**: 环境变量 `SECRET_KEY`
- **认证方式**: Bearer Token

### 数据库用户状态

系统中存在以下用户：
- **admin**: 管理员用户，激活状态
- **demo**: 普通用户，激活状态

### API端点认证状态

| 端点 | 认证要求 | 状态 |
|------|----------|------|
| `POST /api/v1/auth/login` | 无 | ✅ 正常 |
| `POST /api/v1/auth/register` | 无 | ✅ 正常 |
| `GET /api/v1/auth/me` | Bearer Token | ✅ 正常 |
| `GET /api/v1/tasks/` | Bearer Token | ✅ 正常 |
| `POST /api/v1/tasks/` | Bearer Token | ✅ 正常 |
| `GET /api/v1/results/` | Bearer Token | ✅ 已修复 |
| `GET /api/v1/health` | 无 | ✅ 正常 |

## 🧪 测试认证流程

### 1. 手动测试登录

```bash
# 登录获取令牌
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# 预期响应
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 86400,
  "user": {
    "id": "...",
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

### 2. 测试认证访问

```bash
# 使用令牌访问受保护端点
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8000/api/v1/tasks/

# 预期响应
{
  "tasks": [],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 0,
    "pages": 0
  }
}
```

### 3. 使用测试脚本

运行提供的测试脚本：
```bash
python test_auth_flow.py
```

## 🌐 前端认证集成

### 1. 检查前端认证状态

前端应该：
1. 在用户登录后保存JWT令牌到localStorage
2. 在每个API请求中包含Authorization头
3. 处理401/403错误并重定向到登录页面

### 2. 前端认证代码检查

检查 `frontend/src/utils/api.js`：
```javascript
// 请求拦截器应该添加Authorization头
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  }
)
```

### 3. 用户状态管理

检查 `frontend/src/stores/user.js`：
```javascript
// 确保登录后正确保存令牌
const login = async (credentials) => {
  const response = await api.post('/auth/login', credentials)
  const { access_token, user: userData } = response.data
  
  token.value = access_token
  localStorage.setItem('token', access_token)
}
```

## 🚨 常见问题排查

### 1. 403 Forbidden错误

**可能原因**：
- 前端未发送Authorization头
- 令牌已过期
- 令牌格式错误
- 用户账户被禁用

**排查步骤**：
1. 检查浏览器开发者工具的Network标签
2. 确认请求头中包含 `Authorization: Bearer <token>`
3. 验证令牌是否有效（使用jwt.io解码）
4. 检查用户账户状态

### 2. 500 Internal Server Error

**可能原因**：
- API端点代码错误
- 数据库连接问题
- 模型字段不匹配

**排查步骤**：
1. 查看后端服务器日志
2. 检查数据库连接
3. 验证模型定义

### 3. 登录失败

**可能原因**：
- 用户名或密码错误
- 用户账户被禁用
- 密码哈希算法问题

**排查步骤**：
1. 验证用户凭据
2. 检查用户状态
3. 测试密码验证函数

## 🔧 开发者工具

### 1. 认证测试脚本

使用 `test_auth_flow.py` 脚本快速测试认证流程：
```bash
python test_auth_flow.py
```

### 2. 用户管理脚本

创建管理员用户：
```bash
python scripts/create_admin.py
```

### 3. 数据库检查

检查用户数据：
```python
from app.database import SessionLocal
from app.models.user import User

db = SessionLocal()
users = db.query(User).all()
for user in users:
    print(f"用户: {user.username}, 激活: {user.is_active}")
db.close()
```

## 📋 最佳实践

### 1. 前端认证

- 始终检查用户登录状态
- 在路由守卫中验证认证
- 优雅处理认证错误
- 自动刷新过期令牌（如果实现了刷新令牌机制）

### 2. 后端安全

- 使用强密钥生成JWT
- 设置合理的令牌过期时间
- 实现令牌黑名单机制
- 记录认证相关的安全事件

### 3. 错误处理

- 提供清晰的错误消息
- 区分不同类型的认证错误
- 记录详细的错误日志
- 实现用户友好的错误提示

## 🎯 下一步行动

1. **前端开发者**：
   - 检查前端认证流程
   - 确保正确发送Authorization头
   - 实现登录状态管理

2. **后端开发者**：
   - 监控认证相关错误
   - 优化错误处理机制
   - 实现更完善的用户管理

3. **测试团队**：
   - 定期运行认证测试
   - 验证不同用户角色的权限
   - 测试边界情况和错误场景

---

**文档更新时间**: 2024年1月  
**适用版本**: Loop Hole v1.0.0  
**维护者**: 开发团队