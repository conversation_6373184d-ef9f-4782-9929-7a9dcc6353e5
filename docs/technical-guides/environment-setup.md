# Loop Hole 开发环境搭建指南

## 📋 环境要求

- **操作系统**: macOS/Linux/Windows
- **Python**: 3.11+
- **Conda**: Anaconda 或 Miniconda
- **Node.js**: 18+ (用于前端开发)
- **Docker**: 最新版本 (可选，用于数据库)

## 🚀 快速开始

### 第一步：创建Conda环境

```bash
# 1. 创建conda环境（使用Python 3.11）
conda create -n loop_hole python=3.11 -y

# 2. 激活环境
conda activate loop_hole

# 3. 验证Python版本
python --version
# 应该显示: Python 3.11.x
```

> **📝 为什么选择Python 3.11？**
> 
> - **兼容性保证**: 项目依赖（FastAPI、SQLAlchemy、Celery、Playwright）在3.11版本下经过充分测试
> - **Playwright支持**: Playwright对Python 3.11有完整优化，避免网页数据提取功能出现问题
> - **生产稳定性**: 3.11版本在生产环境中广泛使用，稳定性经过验证
> - **性能优势**: 相比3.10有10-60%的性能提升，错误信息更清晰
> - **避免兼容性风险**: 使用最新版本（3.12/3.13）可能导致某些依赖包不兼容或需要从源码编译
> 
> 如果确实需要使用更新版本，建议先用3.11验证项目功能正常后再考虑升级。

### 第二步：进入项目目录

```bash
# 进入项目根目录
cd /Volumes/mini_matrix/github/nick/loop_hole

# 验证项目结构
ls -la
# 应该看到: app/ doc/ frontend/ requirements.txt docker-compose.yml 等
```

### 第三步：安装Python依赖

```bash
# 安装项目依赖包
pip install -r requirements.txt

# 验证关键包安装
python -c "import fastapi; print('✅ FastAPI:', fastapi.__version__)"
python -c "import sqlalchemy; print('✅ SQLAlchemy:', sqlalchemy.__version__)"
python -c "import celery; print('✅ Celery:', celery.__version__)"
python -c "import redis; print('✅ Redis:', redis.__version__)"
```

### 第四步：安装Playwright浏览器

```bash
# 安装Playwright
pip install playwright

# 安装浏览器驱动
playwright install

# 验证安装
python -c "from playwright.sync_api import sync_playwright; print('✅ Playwright安装成功')"
```

### 第五步：设置数据库

#### 选项A: 使用SQLite（开发环境推荐）

```bash
# 创建数据库目录
mkdir -p data

# 设置环境变量
export DATABASE_URL="sqlite:///./data/loop_hole.db"
echo "DATABASE_URL=sqlite:///./data/loop_hole.db" > .env
```

#### 选项B: 使用PostgreSQL（生产环境推荐）

```bash
# 使用Docker启动PostgreSQL
docker run --name loop_hole_postgres \
  -e POSTGRES_DB=loop_hole \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 -d postgres:15

# 等待数据库启动
sleep 10

# 设置环境变量
export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/loop_hole"
echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/loop_hole" > .env
```

### 第六步：初始化数据库

```bash
# 运行数据库迁移
alembic upgrade head

# 验证数据库表创建
python -c "
from app.database import engine
from sqlalchemy import inspect
inspector = inspect(engine)
tables = inspector.get_table_names()
print('✅ 数据库表:', tables)
"
```

### 第七步：启动Redis（可选）

```bash
# 使用Docker启动Redis
docker run --name loop_hole_redis \
  -p 6379:6379 -d redis:7-alpine

# 验证Redis连接
python -c "
import redis
r = redis.Redis(host='localhost', port=6379, db=0)
r.ping()
print('✅ Redis连接成功')
"
```

### 第八步：运行测试

```bash
# 运行API测试脚本
python test_api.py

# 应该看到类似输出:
# 🚀 开始 Loop Hole API 核心功能测试
# 🔍 测试数据库连接...
# ✅ 数据库连接成功
# ...
# 🎉 所有核心功能测试通过！
```

### 第九步：启动开发服务器

```bash
# 启动FastAPI开发服务器
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 服务器启动后访问:
# - API文档: http://localhost:8000/docs
# - 健康检查: http://localhost:8000/api/v1/health
```

## 🔧 前端环境设置（可选）

```bash
# 进入前端目录
cd frontend

# 安装Node.js依赖
npm install

# 启动前端开发服务器
npm run dev

# 前端服务器启动后访问:
# http://localhost:3000
```

## 📝 环境变量配置

创建 `.env` 文件并配置以下变量：

```bash
# 数据库配置
DATABASE_URL=sqlite:///./data/loop_hole.db

# Redis配置
REDIS_URL=redis://localhost:6379/0

# JWT密钥
SECRET_KEY=your-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key

# 应用配置
DEBUG=true
LOG_LEVEL=info

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

## 🐳 Docker开发环境（一键启动）

```bash
# 使用Docker Compose启动完整环境
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f app

# 停止服务
docker-compose -f docker-compose.dev.yml down
```

## ✅ 验证清单

完成环境搭建后，请验证以下功能：

- [ ] Conda环境激活成功
- [ ] Python依赖包安装完成
- [ ] Playwright浏览器安装成功
- [ ] 数据库连接正常
- [ ] Redis连接正常（如果使用）
- [ ] API测试脚本通过
- [ ] 开发服务器启动成功
- [ ] API文档可访问
- [ ] 前端服务器启动成功（如果需要）

## 🛠️ 常用开发命令

```bash
# 激活环境
conda activate loop_hole

# 启动API服务器
python -m uvicorn app.main:app --reload

# 启动Celery Worker
celery -A app.core.scheduler worker --loglevel=info

# 启动Celery Beat
celery -A app.core.scheduler beat --loglevel=info

# 运行数据库迁移
alembic upgrade head

# 创建新的迁移文件
alembic revision --autogenerate -m "描述"

# 运行测试
python test_api.py
pytest tests/

# 代码格式化
black app/
isort app/

# 代码检查
pylint app/
```

## 🔍 故障排除

### 常见问题

1. **ModuleNotFoundError**: 确保conda环境已激活
2. **数据库连接失败**: 检查DATABASE_URL配置
3. **Redis连接失败**: 确保Redis服务已启动
4. **端口占用**: 使用不同端口或停止占用进程
5. **权限问题**: 确保有文件读写权限

### 重置环境

```bash
# 删除conda环境
conda deactivate
conda remove -n loop_hole --all -y

# 重新创建环境
conda create -n loop_hole python=3.11 -y
conda activate loop_hole

# 重新安装依赖
pip install -r requirements.txt
playwright install
```

## 📞 获取帮助

如果遇到问题，请检查：

1. **日志文件**: `logs/` 目录下的日志文件
2. **API文档**: http://localhost:8000/docs
3. **健康检查**: http://localhost:8000/api/v1/health
4. **项目文档**: `doc/` 目录下的相关文档

---

**创建时间**: 2025-08-16 09:38  
**适用版本**: Loop Hole v1.0.0  
**维护者**: 开发团队
