# Loop Hole - 实现指南

## 开发环境搭建

### 1. 系统要求
- Python 3.11+
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+

### 2. 项目初始化
```bash
# 创建项目目录
mkdir loop_hole && cd loop_hole

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 3. 核心依赖
```txt
# requirements.txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
celery==5.3.4
redis==5.0.1
psycopg2-binary==2.9.9
sqlalchemy==2.0.23
alembic==1.12.1
playwright==1.40.0
beautifulsoup4==4.12.2
pandas==2.1.3
numpy==1.25.2
scikit-learn==1.3.2
pydantic==2.5.0
python-jose[cryptography]==3.3.0
python-multipart==0.0.6
structlog==23.2.0
prometheus-client==0.19.0
```

## 项目结构

```
loop_hole/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── config.py              # 配置管理
│   ├── database.py            # 数据库连接
│   ├── models/                # 数据模型
│   │   ├── __init__.py
│   │   ├── task.py
│   │   ├── result.py
│   │   └── user.py
│   ├── api/                   # API路由
│   │   ├── __init__.py
│   │   ├── v1/
│   │   │   ├── __init__.py
│   │   │   ├── tasks.py
│   │   │   ├── extract.py
│   │   │   └── auth.py
│   ├── core/                  # 核心业务逻辑
│   │   ├── __init__.py
│   │   ├── analyzer.py        # 页面分析器
│   │   ├── extractor.py       # 数据提取器
│   │   ├── scheduler.py       # 任务调度器
│   │   └── cache.py           # 缓存管理
│   ├── utils/                 # 工具函数
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── logger.py
│   │   └── validators.py
│   └── tests/                 # 测试文件
├── frontend/                  # 前端代码
│   ├── src/
│   ├── package.json
│   └── vite.config.js
├── docker-compose.yml
├── Dockerfile
├── requirements.txt
└── README.md
```

## 核心模块实现

### 1. 页面分析器 (analyzer.py)
```python
from bs4 import BeautifulSoup
from typing import List, Dict, Any
import re

class PageAnalyzer:
    def __init__(self):
        self.data_patterns = [
            r'\d+',  # 数字
            r'\d{4}-\d{2}-\d{2}',  # 日期
            r'[\d,]+\.?\d*',  # 金额
        ]
    
    def analyze_page(self, html: str) -> Dict[str, Any]:
        soup = BeautifulSoup(html, 'html.parser')
        
        # 识别表格数据
        tables = self._analyze_tables(soup)
        
        # 识别卡片数据
        cards = self._analyze_cards(soup)
        
        # 识别列表数据
        lists = self._analyze_lists(soup)
        
        return {
            'tables': tables,
            'cards': cards,
            'lists': lists,
            'selectors': self._generate_selectors(tables + cards + lists)
        }
    
    def _analyze_tables(self, soup: BeautifulSoup) -> List[Dict]:
        tables = []
        for table in soup.find_all('table'):
            headers = [th.get_text().strip() for th in table.find_all('th')]
            rows = []
            for tr in table.find_all('tr')[1:]:  # 跳过表头
                row = [td.get_text().strip() for td in tr.find_all('td')]
                rows.append(row)
            
            if headers and rows:
                tables.append({
                    'type': 'table',
                    'headers': headers,
                    'rows': rows,
                    'selector': self._get_element_selector(table)
                })
        return tables
```

### 2. 数据提取器 (extractor.py)
```python
from playwright.async_api import async_playwright
import asyncio
from typing import Dict, List, Any

class DataExtractor:
    def __init__(self):
        self.browser_pool = BrowserPool()
    
    async def extract_data(self, url: str, rules: Dict) -> Dict[str, Any]:
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            try:
                await page.goto(url, wait_until='networkidle')
                
                # 等待动态内容加载
                await page.wait_for_timeout(2000)
                
                extracted_data = {}
                
                # 提取表格数据
                if 'tables' in rules:
                    extracted_data['tables'] = await self._extract_tables(page, rules['tables'])
                
                # 提取卡片数据
                if 'cards' in rules:
                    extracted_data['cards'] = await self._extract_cards(page, rules['cards'])
                
                return extracted_data
                
            finally:
                await browser.close()
    
    async def _extract_tables(self, page, table_rules: List[Dict]) -> List[Dict]:
        tables_data = []
        for rule in table_rules:
            selector = rule['selector']
            table_element = await page.query_selector(selector)
            
            if table_element:
                # 提取表头
                headers = await page.evaluate('''
                    (table) => {
                        const ths = table.querySelectorAll('th');
                        return Array.from(ths).map(th => th.textContent.trim());
                    }
                ''', table_element)
                
                # 提取数据行
                rows = await page.evaluate('''
                    (table) => {
                        const trs = table.querySelectorAll('tbody tr');
                        return Array.from(trs).map(tr => {
                            const tds = tr.querySelectorAll('td');
                            return Array.from(tds).map(td => td.textContent.trim());
                        });
                    }
                ''', table_element)
                
                tables_data.append({
                    'headers': headers,
                    'rows': rows,
                    'metadata': rule
                })
        
        return tables_data
```

### 3. 任务调度器 (scheduler.py)
```python
from celery import Celery
from datetime import datetime, timedelta
import json

celery_app = Celery('loop_hole')
celery_app.config_from_object('app.config.CeleryConfig')

@celery_app.task(bind=True, max_retries=3)
def extract_task(self, task_config: Dict):
    try:
        extractor = DataExtractor()
        result = asyncio.run(extractor.extract_data(
            task_config['url'], 
            task_config['rules']
        ))
        
        # 保存结果到数据库
        save_extraction_result(task_config['task_id'], result)
        
        return {'status': 'success', 'data_count': len(result)}
        
    except Exception as exc:
        self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)

@celery_app.task
def schedule_periodic_tasks():
    """定期检查并调度任务"""
    from app.models.task import ExtractionTask
    
    active_tasks = ExtractionTask.get_active_scheduled_tasks()
    
    for task in active_tasks:
        if task.should_run():
            extract_task.delay(task.to_dict())
```

## API接口实现

### 1. 任务管理API (api/v1/tasks.py)
```python
from fastapi import APIRouter, Depends, HTTPException
from app.models.task import ExtractionTask
from app.core.scheduler import extract_task

router = APIRouter(prefix="/tasks", tags=["tasks"])

@router.post("/create")
async def create_task(task_data: TaskCreateRequest):
    task = ExtractionTask.create(
        name=task_data.name,
        url=task_data.url,
        extraction_rules=task_data.rules,
        schedule_config=task_data.schedule
    )
    return {"task_id": task.id, "status": "created"}

@router.get("/{task_id}")
async def get_task(task_id: str):
    task = ExtractionTask.get_by_id(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return task.to_dict()

@router.post("/{task_id}/execute")
async def execute_task(task_id: str):
    task = ExtractionTask.get_by_id(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # 异步执行任务
    job = extract_task.delay(task.to_dict())
    return {"job_id": job.id, "status": "queued"}
```

## 数据库设计实现

### 1. 模型定义 (models/task.py)
```python
from sqlalchemy import Column, String, DateTime, Text, JSON
from sqlalchemy.dialects.postgresql import UUID
from app.database import Base
import uuid

class ExtractionTask(Base):
    __tablename__ = "extraction_tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    url = Column(Text, nullable=False)
    extraction_rules = Column(JSON, nullable=False)
    schedule_config = Column(JSON)
    status = Column(String(50), default='active')
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)
    
    @classmethod
    def create(cls, **kwargs):
        task = cls(**kwargs)
        db.session.add(task)
        db.session.commit()
        return task
    
    def to_dict(self):
        return {
            'id': str(self.id),
            'name': self.name,
            'url': self.url,
            'extraction_rules': self.extraction_rules,
            'schedule_config': self.schedule_config,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
```

## 前端实现

### 1. 主要组件结构
```javascript
// src/components/TaskManager.vue
<template>
  <div class="task-manager">
    <div class="task-list">
      <TaskCard 
        v-for="task in tasks" 
        :key="task.id" 
        :task="task"
        @execute="executeTask"
        @edit="editTask"
      />
    </div>
    <TaskForm @create="createTask" />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { taskApi } from '@/api/tasks'

export default {
  setup() {
    const tasks = ref([])
    
    const loadTasks = async () => {
      try {
        const response = await taskApi.getTasks()
        tasks.value = response.data
      } catch (error) {
        console.error('Failed to load tasks:', error)
      }
    }
    
    const createTask = async (taskData) => {
      try {
        await taskApi.createTask(taskData)
        await loadTasks()
      } catch (error) {
        console.error('Failed to create task:', error)
      }
    }
    
    onMounted(loadTasks)
    
    return {
      tasks,
      createTask,
      executeTask,
      editTask
    }
  }
}
</script>
```

## 部署指南

### 1. Docker部署
```bash
# 构建镜像
docker build -t loop_hole:latest .

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 2. 生产环境配置
```python
# config.py
import os

class ProductionConfig:
    DATABASE_URL = os.getenv('DATABASE_URL')
    REDIS_URL = os.getenv('REDIS_URL')
    SECRET_KEY = os.getenv('SECRET_KEY')
    
    # Celery配置
    CELERY_BROKER_URL = REDIS_URL
    CELERY_RESULT_BACKEND = REDIS_URL
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = 'json'
```

## 测试策略

### 1. 单元测试
```python
# tests/test_analyzer.py
import pytest
from app.core.analyzer import PageAnalyzer

def test_analyze_table():
    html = """
    <table>
        <tr><th>Name</th><th>Value</th></tr>
        <tr><td>Item1</td><td>100</td></tr>
    </table>
    """
    
    analyzer = PageAnalyzer()
    result = analyzer.analyze_page(html)
    
    assert len(result['tables']) == 1
    assert result['tables'][0]['headers'] == ['Name', 'Value']
    assert result['tables'][0]['rows'][0] == ['Item1', '100']
```

### 2. 集成测试
```python
# tests/test_integration.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_create_and_execute_task():
    # 创建任务
    task_data = {
        "name": "Test Task",
        "url": "http://example.com",
        "rules": {"tables": [{"selector": "table"}]}
    }
    
    response = client.post("/api/v1/tasks/create", json=task_data)
    assert response.status_code == 200
    
    task_id = response.json()["task_id"]
    
    # 执行任务
    response = client.post(f"/api/v1/tasks/{task_id}/execute")
    assert response.status_code == 200
```

## 性能优化

### 1. 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_extraction_tasks_status ON extraction_tasks(status);
CREATE INDEX idx_extraction_tasks_created_at ON extraction_tasks(created_at);
CREATE INDEX idx_extraction_results_task_id ON extraction_results(task_id);
CREATE INDEX idx_extraction_results_extracted_at ON extraction_results(extracted_at);
```

### 2. 缓存策略
```python
# utils/cache.py
import redis
import json
from functools import wraps

redis_client = redis.Redis.from_url(os.getenv('REDIS_URL'))

def cache_result(expire_time=3600):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached = redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            redis_client.setex(cache_key, expire_time, json.dumps(result))
            
            return result
        return wrapper
    return decorator
```
