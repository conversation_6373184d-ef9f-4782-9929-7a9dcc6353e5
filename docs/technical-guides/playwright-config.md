# Playwright 浏览器自动化配置指南

## 🎯 概述

Playwright 是 Loop Hole 项目的核心组件，负责智能网页数据提取。本文档详细说明如何正确配置和使用 Playwright。

## 📋 当前状态

### ✅ 已完成配置
- **Chromium 浏览器**: 已安装 (120.0.6099.28)
- **FFMPEG**: 已安装 (用于视频处理)
- **Python 集成**: 已配置 playwright-python
- **异步支持**: 已实现异步上下文管理器

### 🔧 配置文件位置
```
app/core/extractor.py     # 数据提取器主文件
app/api/v1/extract.py     # API 接口
app/core/config.py        # 配置管理
```

## 🚀 安装和配置

### 1. 安装 Playwright
```bash
# 安装 Python 包
uv add playwright

# 安装浏览器 (已完成)
uv run playwright install chromium

# 可选：安装所有浏览器
uv run playwright install  # 包含 Firefox, Safari
```

### 2. 环境变量配置
在 `.env` 文件中添加：
```bash
# Playwright 配置
PLAYWRIGHT_BROWSERS_PATH=/Users/<USER>/Library/Caches/ms-playwright
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000
PLAYWRIGHT_VIEWPORT_WIDTH=1920
PLAYWRIGHT_VIEWPORT_HEIGHT=1080

# 浏览器选择
PLAYWRIGHT_BROWSER=chromium  # chromium, firefox, webkit
```

### 3. 代码配置示例
```python
# app/core/extractor.py
from playwright.async_api import async_playwright

class DataExtractor:
    def __init__(self, headless=True, timeout=30000):
        self.headless = headless
        self.timeout = timeout
        self.playwright = None
        self.browser = None
        self.context = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=self.headless,
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security'
            ]
        )
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)'
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()
```

## 🔧 高级配置

### 1. 浏览器启动参数
```python
# 生产环境推荐配置
launch_options = {
    'headless': True,
    'args': [
        '--no-sandbox',                    # Docker 环境必需
        '--disable-dev-shm-usage',         # 减少内存使用
        '--disable-gpu',                   # 禁用 GPU 加速
        '--disable-web-security',          # 允许跨域请求
        '--disable-features=VizDisplayCompositor',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--memory-pressure-off',           # 关闭内存压力检测
        '--max_old_space_size=4096'        # 限制内存使用
    ]
}
```

### 2. 上下文配置
```python
context_options = {
    'viewport': {'width': 1920, 'height': 1080},
    'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    'locale': 'zh-CN',
    'timezone_id': 'Asia/Shanghai',
    'permissions': ['geolocation'],
    'extra_http_headers': {
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
    }
}
```

### 3. 页面配置
```python
# 页面超时设置
page.set_default_timeout(30000)        # 30秒
page.set_default_navigation_timeout(60000)  # 60秒

# 等待策略
await page.goto(url, wait_until='networkidle')  # 等待网络空闲
await page.wait_for_load_state('domcontentloaded')  # 等待DOM加载
```

## 🐛 常见问题和解决方案

### 1. 浏览器启动失败
```bash
# 错误: Browser executable not found
# 解决: 重新安装浏览器
uv run playwright install chromium

# 错误: Permission denied
# 解决: 检查文件权限
chmod +x ~/.cache/ms-playwright/chromium-*/chrome-mac/Chromium.app/Contents/MacOS/Chromium
```

### 2. 内存使用过高
```python
# 解决方案: 限制并发和内存
async def create_limited_browser():
    return await playwright.chromium.launch(
        headless=True,
        args=[
            '--memory-pressure-off',
            '--max_old_space_size=2048',  # 限制 2GB
            '--disable-dev-shm-usage'
        ]
    )
```

### 3. 网络超时问题
```python
# 增加超时时间和重试机制
async def robust_page_load(page, url, max_retries=3):
    for attempt in range(max_retries):
        try:
            await page.goto(url, timeout=60000, wait_until='networkidle')
            return True
        except TimeoutError:
            if attempt == max_retries - 1:
                raise
            await asyncio.sleep(2 ** attempt)  # 指数退避
    return False
```

## 🚀 性能优化

### 1. 浏览器池管理
```python
class BrowserPool:
    def __init__(self, max_browsers=3):
        self.max_browsers = max_browsers
        self.browsers = []
        self.semaphore = asyncio.Semaphore(max_browsers)
    
    async def get_browser(self):
        async with self.semaphore:
            # 复用浏览器实例
            if self.browsers:
                return self.browsers.pop()
            return await self.create_browser()
```

### 2. 页面缓存策略
```python
# 启用缓存以提高性能
context = await browser.new_context(
    viewport={'width': 1920, 'height': 1080},
    # 启用缓存
    storage_state=None,  # 可以保存登录状态
)
```

### 3. 资源过滤
```python
# 阻止不必要的资源加载
async def block_resources(page):
    await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", 
                    lambda route: route.abort())
```

## 📊 监控和调试

### 1. 启用调试模式
```python
# 开发环境调试
browser = await playwright.chromium.launch(
    headless=False,  # 显示浏览器窗口
    devtools=True,   # 打开开发者工具
    slow_mo=1000     # 慢动作模式
)
```

### 2. 截图和录制
```python
# 截图调试
await page.screenshot(path='debug.png', full_page=True)

# 录制视频
context = await browser.new_context(
    record_video_dir='videos/',
    record_video_size={'width': 1920, 'height': 1080}
)
```

### 3. 性能监控
```python
# 监控页面性能
async def monitor_performance(page):
    # 监听网络请求
    page.on('request', lambda request: print(f'Request: {request.url}'))
    page.on('response', lambda response: print(f'Response: {response.status}'))
    
    # 监听控制台日志
    page.on('console', lambda msg: print(f'Console: {msg.text}'))
```

## 🔒 安全配置

### 1. 沙箱模式
```python
# 生产环境安全配置
browser = await playwright.chromium.launch(
    headless=True,
    args=[
        '--no-sandbox',  # 仅在 Docker 中使用
        '--disable-web-security',  # 谨慎使用
    ]
)
```

### 2. 用户代理轮换
```python
user_agents = [
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
]

context = await browser.new_context(
    user_agent=random.choice(user_agents)
)
```

## 📝 最佳实践

### 1. 资源管理
- ✅ 始终使用异步上下文管理器
- ✅ 及时关闭页面、上下文和浏览器
- ✅ 使用连接池管理多个浏览器实例
- ✅ 监控内存使用情况

### 2. 错误处理
- ✅ 实现重试机制
- ✅ 捕获和记录异常
- ✅ 设置合理的超时时间
- ✅ 优雅降级处理

### 3. 性能优化
- ✅ 阻止不必要的资源加载
- ✅ 使用适当的等待策略
- ✅ 复用浏览器实例
- ✅ 启用缓存机制

---
*配置指南更新时间: 2025-01-11*  
*适用版本: Playwright 1.40+, Python 3.12+*
