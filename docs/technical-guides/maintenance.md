# Loop Hole - 运维指南

## 系统监控

### 1. 关键指标监控

#### 应用层指标
```python
# Prometheus指标定义
from prometheus_client import Counter, Histogram, Gauge

# 任务执行指标
task_executions_total = Counter('task_executions_total', 'Total task executions', ['status'])
task_duration_seconds = Histogram('task_duration_seconds', 'Task execution duration')
active_tasks_gauge = Gauge('active_tasks', 'Number of active tasks')

# 数据提取指标
extractions_total = Counter('extractions_total', 'Total extractions', ['source'])
extraction_errors_total = Counter('extraction_errors_total', 'Total extraction errors', ['error_type'])
data_points_extracted = Counter('data_points_extracted_total', 'Total data points extracted')

# 系统资源指标
browser_pool_size = Gauge('browser_pool_size', 'Browser pool size')
database_connections = Gauge('database_connections', 'Database connections')
redis_memory_usage = Gauge('redis_memory_usage_bytes', 'Redis memory usage')
```

#### 基础设施指标
- **CPU使用率**: < 80%
- **内存使用率**: < 85%
- **磁盘使用率**: < 90%
- **网络延迟**: < 100ms
- **数据库连接数**: < 80% 最大连接数

### 2. 告警规则

```yaml
# alerting_rules.yml
groups:
- name: loop_hole_alerts
  rules:
  - alert: HighTaskFailureRate
    expr: rate(task_executions_total{status="failed"}[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High task failure rate detected"
      description: "Task failure rate is {{ $value }} per second"

  - alert: DatabaseConnectionsHigh
    expr: database_connections > 80
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Database connections high"
      description: "Database connections: {{ $value }}"

  - alert: ExtractionServiceDown
    expr: up{job="loop_hole_api"} == 0
    for: 30s
    labels:
      severity: critical
    annotations:
      summary: "Loop Hole API service is down"
```

### 3. 健康检查

```python
# app/api/v1/health.py
from fastapi import APIRouter, HTTPException
from app.database import get_db_session
from app.core.cache import redis_client
import asyncio

router = APIRouter()

@router.get("/health")
async def health_check():
    checks = {
        "database": await check_database(),
        "redis": await check_redis(),
        "celery": await check_celery(),
        "browser_pool": await check_browser_pool()
    }
    
    all_healthy = all(checks.values())
    status_code = 200 if all_healthy else 503
    
    return {
        "status": "healthy" if all_healthy else "unhealthy",
        "checks": checks,
        "timestamp": datetime.utcnow().isoformat()
    }

async def check_database():
    try:
        async with get_db_session() as session:
            await session.execute("SELECT 1")
        return True
    except Exception:
        return False

async def check_redis():
    try:
        await redis_client.ping()
        return True
    except Exception:
        return False
```

## 日志管理

### 1. 日志配置

```python
# app/utils/logger.py
import structlog
import logging
from pythonjsonlogger import jsonlogger

def configure_logging():
    # 配置结构化日志
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # 配置标准库日志
    handler = logging.StreamHandler()
    formatter = jsonlogger.JsonFormatter(
        '%(asctime)s %(name)s %(levelname)s %(message)s'
    )
    handler.setFormatter(formatter)
    
    root_logger = logging.getLogger()
    root_logger.addHandler(handler)
    root_logger.setLevel(logging.INFO)
```

### 2. 日志分类

#### 应用日志
```python
# 任务执行日志
logger.info("task_started", 
           task_id=task_id, 
           url=url, 
           user_id=user_id)

logger.info("task_completed", 
           task_id=task_id, 
           duration=duration, 
           data_count=data_count)

logger.error("task_failed", 
            task_id=task_id, 
            error=str(error), 
            retry_count=retry_count)

# 数据提取日志
logger.info("extraction_started", 
           url=url, 
           rules_count=len(rules))

logger.info("extraction_completed", 
           url=url, 
           extracted_items=item_count, 
           duration=duration)
```

#### 安全日志
```python
# 认证日志
logger.info("user_login", 
           user_id=user_id, 
           ip_address=request.client.host)

logger.warning("invalid_token", 
              token_hash=token_hash[:8], 
              ip_address=request.client.host)

# 权限日志
logger.warning("access_denied", 
              user_id=user_id, 
              resource=resource, 
              action=action)
```

### 3. 日志轮转和归档

```yaml
# docker-compose.yml 日志配置
version: '3.8'
services:
  api:
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
        
  # 使用 Fluentd 收集日志
  fluentd:
    image: fluent/fluentd:v1.16
    volumes:
      - ./fluentd.conf:/fluentd/etc/fluent.conf
      - /var/log/loop_hole:/var/log/loop_hole
```

## 备份策略

### 1. 数据库备份

```bash
#!/bin/bash
# scripts/backup_database.sh

BACKUP_DIR="/backups/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="loophole"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME \
  --format=custom \
  --compress=9 \
  --file="$BACKUP_DIR/loophole_$DATE.dump"

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.dump" -mtime +30 -delete

# 验证备份
pg_restore --list "$BACKUP_DIR/loophole_$DATE.dump" > /dev/null
if [ $? -eq 0 ]; then
    echo "Backup successful: loophole_$DATE.dump"
else
    echo "Backup verification failed!"
    exit 1
fi
```

### 2. 配置备份

```bash
#!/bin/bash
# scripts/backup_configs.sh

BACKUP_DIR="/backups/configs"
DATE=$(date +%Y%m%d_%H%M%S)

# 备份配置文件
tar -czf "$BACKUP_DIR/configs_$DATE.tar.gz" \
  docker-compose.yml \
  .env \
  nginx.conf \
  prometheus.yml \
  alertmanager.yml

# 清理旧备份
find $BACKUP_DIR -name "configs_*.tar.gz" -mtime +7 -delete
```

### 3. 自动化备份

```yaml
# k8s/cronjob-backup.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: database-backup
spec:
  schedule: "0 2 * * *"  # 每天凌晨2点
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup
            image: postgres:15
            command:
            - /bin/bash
            - -c
            - |
              pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME \
                --format=custom --compress=9 \
                --file="/backup/loophole_$(date +%Y%m%d_%H%M%S).dump"
            env:
            - name: DB_HOST
              value: "postgresql-service"
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: username
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: password
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure
```

## 故障排除

### 1. 常见问题诊断

#### 任务执行失败
```bash
# 检查Celery队列状态
celery -A app.celery inspect active
celery -A app.celery inspect reserved
celery -A app.celery inspect stats

# 检查Redis连接
redis-cli ping
redis-cli info memory

# 检查数据库连接
psql -h localhost -U user -d loophole -c "SELECT version();"
```

#### 页面提取失败
```python
# 调试页面分析
from app.core.analyzer import PageAnalyzer
from app.core.extractor import DataExtractor

# 测试页面分析
analyzer = PageAnalyzer()
result = analyzer.analyze_page(html_content)
print(json.dumps(result, indent=2))

# 测试数据提取
extractor = DataExtractor()
data = await extractor.extract_data(url, rules)
print(json.dumps(data, indent=2))
```

#### 性能问题
```sql
-- 检查慢查询
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- 检查数据库连接
SELECT count(*) as connection_count, state
FROM pg_stat_activity
GROUP BY state;

-- 检查表大小
SELECT schemaname, tablename, 
       pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### 2. 故障恢复流程

#### 数据库故障恢复
```bash
# 1. 停止应用服务
docker-compose stop api worker

# 2. 恢复数据库
pg_restore -h localhost -U user -d loophole_new \
  --clean --if-exists /backups/loophole_20231201_020000.dump

# 3. 验证数据完整性
psql -h localhost -U user -d loophole_new -c "
  SELECT COUNT(*) FROM extraction_tasks;
  SELECT COUNT(*) FROM extraction_results;
"

# 4. 重启服务
docker-compose up -d
```

#### 缓存故障恢复
```bash
# 清理Redis缓存
redis-cli FLUSHALL

# 重启Redis
docker-compose restart redis

# 预热关键缓存
curl -X POST http://localhost:8000/api/v1/cache/warmup
```

## 性能优化

### 1. 数据库优化

```sql
-- 定期维护
VACUUM ANALYZE extraction_tasks;
VACUUM ANALYZE extraction_results;

-- 重建索引
REINDEX INDEX idx_extraction_tasks_status;
REINDEX INDEX idx_extraction_results_task_id;

-- 分区表维护
CREATE TABLE extraction_results_2024_01 PARTITION OF extraction_results
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### 2. 缓存优化

```python
# 缓存预热策略
async def warmup_cache():
    # 预热热门任务配置
    popular_tasks = await get_popular_tasks()
    for task in popular_tasks:
        cache_key = f"task_config:{task.id}"
        await redis_client.setex(cache_key, 3600, json.dumps(task.to_dict()))
    
    # 预热页面分析结果
    recent_urls = await get_recent_analyzed_urls()
    for url in recent_urls:
        analysis_result = await get_analysis_result(url)
        cache_key = f"page_analysis:{hash(url)}"
        await redis_client.setex(cache_key, 1800, json.dumps(analysis_result))
```

### 3. 系统调优

```yaml
# docker-compose.yml 资源限制
version: '3.8'
services:
  api:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    environment:
      - UVICORN_WORKERS=4
      - UVICORN_MAX_REQUESTS=1000
      
  worker:
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 4G
    environment:
      - CELERY_CONCURRENCY=8
      - CELERY_MAX_TASKS_PER_CHILD=100
```

## 安全维护

### 1. 定期安全检查

```bash
#!/bin/bash
# scripts/security_check.sh

# 检查过期的JWT Token
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
  SELECT COUNT(*) as expired_tokens
  FROM user_sessions 
  WHERE expires_at < NOW();
"

# 检查异常登录
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
  SELECT user_id, ip_address, COUNT(*) as login_attempts
  FROM auth_logs 
  WHERE created_at > NOW() - INTERVAL '1 hour'
    AND status = 'failed'
  GROUP BY user_id, ip_address
  HAVING COUNT(*) > 5;
"

# 检查权限异常
grep "access_denied" /var/log/loop_hole/app.log | tail -20
```

### 2. 证书更新

```bash
#!/bin/bash
# scripts/update_certificates.sh

# 使用Let's Encrypt更新证书
certbot renew --quiet

# 重启Nginx
docker-compose restart nginx

# 验证证书
openssl x509 -in /etc/letsencrypt/live/yourdomain.com/cert.pem -text -noout
```

### 3. 依赖更新

```bash
# 检查Python依赖安全漏洞
pip-audit

# 更新依赖
pip-compile --upgrade requirements.in
pip install -r requirements.txt

# 检查Docker镜像漏洞
docker scan loop_hole:latest
```

## 容量规划

### 1. 存储容量规划

```python
# 数据增长预估
def estimate_storage_growth():
    # 每个任务平均数据大小
    avg_task_data_size = 50 * 1024  # 50KB
    
    # 每日任务执行次数
    daily_executions = 1000
    
    # 数据保留期
    retention_days = 365
    
    # 预估存储需求
    daily_storage = avg_task_data_size * daily_executions
    total_storage = daily_storage * retention_days
    
    return {
        'daily_storage_mb': daily_storage / (1024 * 1024),
        'total_storage_gb': total_storage / (1024 * 1024 * 1024)
    }
```

### 2. 计算资源规划

```yaml
# 资源需求评估
resources:
  api_server:
    cpu: "2 cores per 100 concurrent users"
    memory: "4GB base + 100MB per concurrent user"
    
  worker_nodes:
    cpu: "1 core per 10 concurrent extractions"
    memory: "2GB base + 500MB per browser instance"
    
  database:
    cpu: "4 cores minimum"
    memory: "8GB + 25% of data size"
    storage: "SSD, 3x data size for indexes and temp"
    
  redis:
    memory: "2GB + cache size"
```

## 运维自动化

### 1. 部署自动化

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Build and push Docker image
      run: |
        docker build -t loop_hole:${{ github.sha }} .
        docker push registry.example.com/loop_hole:${{ github.sha }}
    
    - name: Deploy to Kubernetes
      run: |
        kubectl set image deployment/loop_hole-api \
          api=registry.example.com/loop_hole:${{ github.sha }}
        kubectl rollout status deployment/loop_hole-api
```

### 2. 监控自动化

```python
# scripts/auto_scaling.py
import asyncio
from kubernetes import client, config

async def auto_scale_workers():
    config.load_incluster_config()
    apps_v1 = client.AppsV1Api()
    
    # 获取队列长度
    queue_length = await get_celery_queue_length()
    
    # 计算所需worker数量
    desired_replicas = min(max(queue_length // 10, 2), 20)
    
    # 更新deployment
    apps_v1.patch_namespaced_deployment_scale(
        name="loop_hole-worker",
        namespace="default",
        body={"spec": {"replicas": desired_replicas}}
    )
```
