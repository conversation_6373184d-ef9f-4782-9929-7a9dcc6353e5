# 技术指南

本目录包含面向开发者和运维人员的技术实现指南。

## 📚 文档列表

- [**系统架构**](./architecture.md) - 整体架构设计说明
- [**环境搭建**](./environment-setup.md) - 开发和部署环境配置
- [**运维操作**](./maintenance.md) - 系统维护和运维指南
- [**Playwright配置**](./playwright-config.md) - 浏览器自动化配置
- [**认证问题排查**](./auth-troubleshooting.md) - 认证相关问题解决
- [**数据库指南**](./database-guide.md) - 数据库选择和配置
- [**实现细节**](./implementation.md) - 核心功能实现说明

## 🎯 适用人群

- 后端开发者
- 前端开发者
- 系统运维人员
- DevOps工程师

## 🔧 快速导航

- **新手开发者**: 建议先阅读[环境搭建](./environment-setup.md)和[系统架构](./architecture.md)
- **运维人员**: 重点关注[运维操作](./maintenance.md)和相关配置文档
- **问题排查**: 查看对应的故障排除指南
