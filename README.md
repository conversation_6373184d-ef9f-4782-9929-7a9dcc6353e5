# Loop Hole - 智能网页数据提取系统

Loop Hole 是一个智能的网页数据提取系统，能够自动分析复杂的管理后台网页，提取业务数据，减少数据库负载，提高数据获取效率。

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Python](https://img.shields.io/badge/python-3.11+-green.svg)](https://python.org)
[![Docker](https://img.shields.io/badge/docker-supported-blue.svg)](https://docker.com)

## 🚀 功能特性

- **🔍 智能页面分析**: 自动识别网页中的表格、卡片、列表等数据结构
- **🤖 自动数据提取**: 使用Playwright进行浏览器自动化，支持JavaScript渲染的动态内容
- **⏰ 任务调度**: 基于Celery的分布式任务队列，支持定时和触发式数据提取
- **💾 数据缓存**: Redis缓存策略，提高数据访问效率
- **🌐 RESTful API**: 完整的API接口，支持任务管理、数据提取和结果查询
- **🎨 Web管理界面**: 基于Vue.js的现代化管理界面
- **📊 多格式导出**: 支持JSON、CSV、Excel等多种数据导出格式
- **📈 实时监控**: 系统健康状态和任务执行进度实时监控

## 🏗️ 技术架构

- **后端**: Python 3.11+ + FastAPI + SQLAlchemy + Alembic
- **前端**: Vue.js 3 + Element Plus + Vite + ECharts
- **数据库**: PostgreSQL 13+ (开发环境可使用 SQLite)
- **缓存**: Redis 6.0+
- **任务队列**: Celery + Redis
- **浏览器自动化**: Playwright
- **容器化**: Docker + Docker Compose
- **监控**: Prometheus + 自定义监控面板

## ⚡ 快速启动 (推荐)

### 一键启动脚本

**Linux/macOS:**
```bash
# 克隆项目
git clone <repository-url>
cd loop_hole

# 运行自动化启动脚本
chmod +x quick_start.sh
./quick_start.sh
```

**Windows:**
```batch
# 克隆项目
git clone <repository-url>
cd loop_hole

# 运行Windows启动脚本
quick_start.bat
```

### 脚本功能
- ✅ 自动检查系统环境 (Docker, Docker Compose)
- ✅ 自动创建配置文件
- ✅ 拉取和构建所需镜像
- ✅ 启动所有服务 (数据库、缓存、后端、前端)
- ✅ 初始化数据库
- ✅ 验证系统健康状态
- ✅ 显示访问地址和使用指南

**启动完成后访问:**
- 🌐 **前端界面**: http://localhost
- 📚 **API文档**: http://localhost:8000/docs
- 🔧 **API接口**: http://localhost:8000

## 📚 详细文档

### 🎯 新用户指南
- [**完整使用指南**](docs/user_guide.md) - 从零开始的详细教程
- [**项目执行计划**](docs/project_execution_plan.md) - 开发进度和功能规划
- [**故障排除指南**](docs/troubleshooting.md) - 常见问题解决方案

### 👩‍💻 开发者文档
- [**API 文档**](http://localhost:8000/docs) - 完整的 REST API 接口文档
- [**系统架构设计**](docs/architecture.md) - 技术架构详解
- [**开发环境搭建**](docs/development.md) - 本地开发指南
- [**开发计划**](docs/development-plans/) - 待完成功能和改进计划

## 🔧 手动安装 (开发者)

### 环境要求
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **Docker**: 20.0+ 和 Docker Compose 2.0+
- **内存**: 4GB+ (推荐 8GB+)
- **硬盘**: 5GB+ 可用空间

### 1. 获取代码
```bash
git clone <repository-url>
cd loop_hole
```

### 2. Docker 部署 (推荐)
```bash
# 开发环境
docker-compose -f docker-compose.dev.yml up -d

# 生产环境
docker-compose up -d
```

### 3. 源码部署
```bash
# 1. 后端依赖
pip install -r requirements.txt
playwright install

# 2. 前端依赖
cd frontend && npm install

# 3. 启动服务
# 数据库和缓存
docker-compose -f docker-compose.dev.yml up postgres redis -d

# 数据库迁移
alembic upgrade head

# 启动后端 (新终端)
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动任务队列 (新终端)
celery -A app.core.tasks worker --loglevel=info

# 启动前端 (新终端)
cd frontend && npm run dev
```

## 🎮 快速使用教程

### 创建第一个数据提取任务

1. **访问系统**: 打开浏览器访问 http://localhost
2. **创建任务**: 点击"任务管理" → "创建新任务"
3. **配置提取**:
   ```
   任务名称: 测试任务
   目标URL: https://example.com/data-table
   提取类型: 自动识别 (推荐新手)
   识别元素: ☑️ 表格数据 ☑️ 卡片数据
   ```
4. **测试配置**: 点击"测试提取"验证结果
5. **保存执行**: 保存任务并点击"执行"

### 查看提取结果
- **实时监控**: 在"仪表板"查看任务执行状态
- **结果查看**: 在"结果查看"中查看和导出数据
- **数据导出**: 支持 JSON、CSV、Excel 格式导出

## 🔍 系统监控和维护

### 查看系统状态
```bash
# 检查所有服务状态
./quick_start.sh --status

# 查看实时日志
./quick_start.sh --logs

# 重启服务
./quick_start.sh --restart

# 停止服务
./quick_start.sh --stop
```

### 健康检查
```bash
# 运行健康检查脚本
bash scripts/health_check.sh

# 或手动检查
curl http://localhost:8000/api/v1/health
```

### 常用管理命令

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DATABASE_URL` | PostgreSQL连接URL | `postgresql://postgres:postgres@localhost:5432/loop_hole` |
| `REDIS_URL` | Redis连接URL | `redis://localhost:6379/0` |
| `SECRET_KEY` | 应用密钥 | - |
| `JWT_SECRET_KEY` | JWT密钥 | - |
| `DEBUG` | 调试模式 | `false` |
| `LOG_LEVEL` | 日志级别 | `info` |

### 数据库配置

系统使用PostgreSQL作为主数据库，支持以下特性：
- UUID主键
- JSONB字段存储复杂数据
- 自动时间戳
- 索引优化

### 缓存配置

Redis用于以下场景：
- 页面分析结果缓存
- 数据提取结果缓存
- 用户会话管理
- Celery任务队列

## 📖 使用指南

### 1. 创建提取任务

```python
# 通过API创建任务
POST /api/v1/tasks
{
    "name": "电商数据提取",
    "url": "https://example.com/admin/products",
    "extraction_rules": {
        "products": {
            "type": "table",
            "selector": "#products-table",
            "fields": {
                "name": "td:nth-child(1)",
                "price": "td:nth-child(2)",
                "stock": "td:nth-child(3)"
            }
        }
    },
    "schedule_config": {
        "type": "interval",
        "interval_minutes": 60
    }
}
```

### 2. 页面分析

```python
# 自动分析页面结构
POST /api/v1/extract/analyze
{
    "url": "https://example.com/admin/dashboard"
}
```

### 3. 立即执行提取

```python
# 立即执行数据提取
POST /api/v1/extract/execute
{
    "url": "https://example.com/admin/products",
    "extraction_rules": { ... }
}
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
make test

# 运行特定测试
python -m pytest tests/test_integration.py -v

# 运行前端测试
cd frontend && npm run test
```

### 测试覆盖率

```bash
python -m pytest --cov=app tests/
```

## 📊 监控和日志

### 日志配置

系统使用结构化日志，支持：
- JSON格式输出
- 多级别日志
- 请求追踪
- 错误堆栈

### 健康检查

- API健康检查: `GET /api/v1/health`
- 数据库连接检查
- Redis连接检查
- Celery队列状态

### 性能监控

- 任务执行统计
- API响应时间
- 数据库查询性能
- 缓存命中率

## 🔒 安全特性

- JWT身份认证
- 基于角色的访问控制
- HTTPS/TLS加密传输
- SQL注入防护
- XSS防护
- CSRF保护

## 🚀 部署指南

### Docker部署

1. **构建镜像**
```bash
make build
```

2. **启动服务**
```bash
make prod
```

3. **查看日志**
```bash
make logs
```

### 生产环境配置

- 使用强密码和密钥
- 配置HTTPS证书
- 设置防火墙规则
- 配置备份策略
- 监控系统资源

## 🛠️ 开发指南

### 代码规范

```bash
# 代码格式化
make format

# 代码检查
make lint
```

### 数据库迁移

```bash
# 创建迁移
alembic revision --autogenerate -m "描述"

# 执行迁移
make migrate
```

### 添加新功能

1. 创建数据模型 (`app/models/`)
2. 定义API路由 (`app/api/v1/`)
3. 实现业务逻辑 (`app/core/`)
4. 添加测试 (`tests/`)
5. 更新文档

## 📚 API文档

完整的API文档可在以下地址查看：
- 开发环境: http://localhost:8000/docs
- 生产环境: http://localhost/api/v1/docs

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务状态
   - 验证连接字符串
   - 确认网络连通性

2. **Redis连接失败**
   - 检查Redis服务状态
   - 验证Redis配置
   - 检查端口占用

3. **Playwright浏览器启动失败**
   - 安装浏览器依赖
   - 检查系统权限
   - 验证Docker配置

4. **前端构建失败**
   - 清除node_modules
   - 重新安装依赖
   - 检查Node.js版本

### 获取帮助

- 查看日志: `make logs`
- 进入容器: `make shell`
- 检查服务状态: `docker-compose ps`

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件
- 技术讨论群

---

**Loop Hole** - 让数据提取变得简单高效！
